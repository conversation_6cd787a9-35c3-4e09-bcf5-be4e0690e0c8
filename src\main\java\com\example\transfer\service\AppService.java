package com.example.transfer.service;

import com.example.transfer.config.AppContext;
import com.example.transfer.dto.SocketCredential;
import com.example.transfer.dto.Status;
import com.example.transfer.dto.request.TransferRequest;
import com.example.transfer.service.cronjob.TaskId;
import com.example.transfer.service.cronjob.TaskManager;
import com.example.transfer.websocket.NamAWebsocket;
import com.example.transfer.websocket.OtpWebsocket;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class AppService {
    private final NamABankService namABankService;
    private final OtpWebsocket otpWebsocket;
    private final NamAWebsocket namAWebsocket;
    private final TaskManager taskManager;
    private final DequeService dequeService;
    private long lastOfferTime = 0;

    @EventListener(ContextRefreshedEvent.class)
    @Async
    public void onApplicationEvent() {
        start();
    }

    public void start() {
        otpWebsocket.connect();
       SocketCredential credential = this.login();
       if (!credential.isCredentialValid()) {
           log.info("Login failed");
           return;
       }
        namAWebsocket.connect(credential.getSocketName(), credential.getSign());
       taskManager.startTask(TaskId.PING, namAWebsocket::ping, 10, 10);
       taskManager.startTask(TaskId.FETCH_TRANSACTION, namABankService::getTransactions, 100, 60);
       taskManager.startTask(TaskId.HANDLE_QUEUE, this::handleQueue, 2, 11);
    }

    private SocketCredential login() {
        int maxRetries = 3;
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            SocketCredential credential = namABankService.login();
            if (credential.isCredentialValid()) {
                return credential;
            }
            log.info("Login failed on attempt {}", attempt + 1);
        }

        log.info("All login attempts failed");
        return new SocketCredential();

    }

    public void restartNamA() {
        SocketCredential credential = this.login();
        if (!credential.isCredentialValid()) {
            log.info("Login failed");
            return;
        }
        namAWebsocket.connect(credential.getSocketName(), credential.getSign());
        taskManager.restartTask(TaskId.PING, 10, 10);
        taskManager.restartTask(TaskId.FETCH_TRANSACTION, 100, 60);
    }

    public void handleQueue() {
        if (dequeService.isEmpty()) {
            return;
        }
        long currentTime = System.currentTimeMillis();
        if (!AppContext.isTimeToTransfer() || currentTime - lastOfferTime < 60000) {
            return;
        }
        if (AppContext.getStatus() != Status.WAITING.ordinal()) {
            log.warn("Skip transfer. Status is not waiting");
            // TODO: bot tele
            return;
        }

        if (!namABankService.getIsLogin()) {
            restartNamA();
        }
        if (!namABankService.getIsLogin()) {
            log.warn("Login failed. Retry in 60 seconds");
            return;
        }

        TransferRequest transferRequest = dequeService.poll();
        log.info("Handling transfer request: {}", transferRequest);
        lastOfferTime = currentTime;
        AppContext.setFromRequest(transferRequest);
        namABankService.transfer(transferRequest);
    }
}
