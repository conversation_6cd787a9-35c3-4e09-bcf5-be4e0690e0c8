package model

// APIResponse standard API response format
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// ErrorResponse for error responses
type ErrorResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// TransferResponse for transfer operation response
type TransferResponse struct {
	TransferID string `json:"transferId"`
	Status     string `json:"status"`
	Message    string `json:"message"`
}

// QueueStatusResponse for queue status
type QueueStatusResponse struct {
	QueueSize int `json:"queueSize"`
	IsEmpty   bool `json:"isEmpty"`
}
