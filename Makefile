# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=transfer-service
BINARY_UNIX=$(BINARY_NAME)_unix

# Docker parameters
DOCKER_IMAGE=transfer-service
DOCKER_TAG=latest

.PHONY: all build clean test deps docker-build docker-run docker-stop help

all: test build

build:
	$(GOBUILD) -o $(BINARY_NAME) -v cmd/server/main.go

build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v cmd/server/main.go

clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

test:
	$(GOTEST) -v ./...

test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

deps:
	$(GOMOD) download
	$(GOMOD) tidy

run:
	$(GOBUILD) -o $(BINARY_NAME) -v cmd/server/main.go
	./$(BINARY_NAME)

# Docker commands
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

docker-run:
	docker-compose up -d

docker-stop:
	docker-compose down

docker-logs:
	docker-compose logs -f transfer-service

docker-clean:
	docker-compose down -v
	docker rmi $(DOCKER_IMAGE):$(DOCKER_TAG) || true

# Development commands
dev:
	$(GOCMD) run cmd/server/main.go

dev-watch:
	air

lint:
	golangci-lint run

format:
	$(GOCMD) fmt ./...

# Database commands
redis-cli:
	docker-compose exec redis redis-cli

# Help
help:
	@echo "Available commands:"
	@echo "  build         - Build the binary"
	@echo "  build-linux   - Build the binary for Linux"
	@echo "  clean         - Clean build files"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  deps          - Download dependencies"
	@echo "  run           - Build and run the application"
	@echo "  dev           - Run in development mode"
	@echo "  dev-watch     - Run with hot reload (requires air)"
	@echo "  lint          - Run linter"
	@echo "  format        - Format code"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run with Docker Compose"
	@echo "  docker-stop   - Stop Docker Compose"
	@echo "  docker-logs   - View Docker logs"
	@echo "  docker-clean  - Clean Docker resources"
	@echo "  redis-cli     - Connect to Redis CLI"
	@echo "  help          - Show this help message"
