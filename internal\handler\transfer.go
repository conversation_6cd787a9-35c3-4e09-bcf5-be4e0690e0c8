package handler

import (
	"net/http"

	"transfer-service/internal/model"
	"transfer-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type TransferHandler struct {
	transferService *service.TransferService
	bankService     *service.BankService
	apiKey          string
	validator       *validator.Validate
}

func NewTransferHandler(transferService *service.TransferService, bankService *service.BankService, apiKey string) *TransferHandler {
	return &TransferHandler{
		transferService: transferService,
		bankService:     bankService,
		apiKey:          apiKey,
		validator:       validator.New(),
	}
}

// Transfer handles POST /api/transfer
func (h *TransferHandler) Transfer(c *gin.Context) {
	// Check API key
	headerApiKey := c.GetHeader("X-Api-Key")
	if headerApiKey != h.apiKey {
		logrus.Warnf("Invalid API key provided: %s", headerApiKey)
		c.JSO<PERSON>(http.StatusUnauthorized, model.APIResponse{
			Success: false,
			Message: "Unauthorized",
			Error:   "Invalid API key",
		})
		return
	}

	// Parse request body
	var transferRequest model.TransferRequest
	if err := c.ShouldBindJSON(&transferRequest); err != nil {
		logrus.Errorf("Failed to bind JSON: %v", err)
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success: false,
			Message: "Invalid request format",
			Error:   err.Error(),
		})
		return
	}

	// Validate request
	if err := h.validator.Struct(&transferRequest); err != nil {
		logrus.Errorf("Validation failed: %v", err)
		c.JSON(http.StatusBadRequest, model.APIResponse{
			Success: false,
			Message: "Validation failed",
			Error:   err.Error(),
		})
		return
	}

	logrus.Infof("Transfer request received: %+v", transferRequest)

	// Queue the transfer request
	err := h.transferService.QueueTransfer(&transferRequest)
	if err != nil {
		logrus.Errorf("Failed to queue transfer: %v", err)
		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Message: "Failed to queue transfer",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Transfer request queued successfully",
		Data: model.TransferResponse{
			TransferID: "", // Could generate a UUID here
			Status:     "queued",
			Message:    "Transfer request has been queued for processing",
		},
	})
}

// Test handles GET /api/transfer/test
func (h *TransferHandler) Test(c *gin.Context) {
	logrus.Info("Test endpoint called")
	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Test endpoint working",
		Data:    "Test",
	})
}

// GetQueueStatus handles GET /api/transfer/queue/status
func (h *TransferHandler) GetQueueStatus(c *gin.Context) {
	status, err := h.transferService.GetQueueStatus()
	if err != nil {
		logrus.Errorf("Failed to get queue status: %v", err)
		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Message: "Failed to get queue status",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Queue status retrieved successfully",
		Data:    status,
	})
}

// GetQueuedTransfers handles GET /api/transfer/queue
func (h *TransferHandler) GetQueuedTransfers(c *gin.Context) {
	transfers, err := h.transferService.GetAllQueuedTransfers()
	if err != nil {
		logrus.Errorf("Failed to get queued transfers: %v", err)
		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Message: "Failed to get queued transfers",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Queued transfers retrieved successfully",
		Data:    transfers,
	})
}

// ProcessQueue handles POST /api/transfer/queue/process
func (h *TransferHandler) ProcessQueue(c *gin.Context) {
	// Check API key
	headerApiKey := c.GetHeader("X-Api-Key")
	if headerApiKey != h.apiKey {
		logrus.Warnf("Invalid API key provided: %s", headerApiKey)
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success: false,
			Message: "Unauthorized",
			Error:   "Invalid API key",
		})
		return
	}

	err := h.transferService.ProcessQueuedTransfers()
	if err != nil {
		logrus.Errorf("Failed to process queue: %v", err)
		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Message: "Failed to process queue",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Queue processed successfully",
	})
}

// ClearQueue handles DELETE /api/transfer/queue
func (h *TransferHandler) ClearQueue(c *gin.Context) {
	// Check API key
	headerApiKey := c.GetHeader("X-Api-Key")
	if headerApiKey != h.apiKey {
		logrus.Warnf("Invalid API key provided: %s", headerApiKey)
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success: false,
			Message: "Unauthorized",
			Error:   "Invalid API key",
		})
		return
	}

	err := h.transferService.ClearQueue()
	if err != nil {
		logrus.Errorf("Failed to clear queue: %v", err)
		c.JSON(http.StatusInternalServerError, model.APIResponse{
			Success: false,
			Message: "Failed to clear queue",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Queue cleared successfully",
	})
}

// Login handles POST /api/transfer/login - Manual login trigger
func (h *TransferHandler) Login(c *gin.Context) {
	// Check API key
	headerApiKey := c.GetHeader("X-Api-Key")
	if headerApiKey != h.apiKey {
		logrus.Warnf("Invalid API key provided: %s", headerApiKey)
		c.JSON(http.StatusUnauthorized, model.APIResponse{
			Success: false,
			Message: "Unauthorized",
			Error:   "Invalid API key",
		})
		return
	}

	logrus.Info("Manual login triggered")

	// Try browser automation login first
	err := h.bankService.AutoLogin()
	if err != nil {
		logrus.Warnf("Browser automation login failed: %v, trying API login", err)

		// Fallback to API login
		err = h.bankService.LoginWithCredentials()
		if err != nil {
			logrus.Errorf("Both login methods failed: %v", err)
			c.JSON(http.StatusInternalServerError, model.APIResponse{
				Success: false,
				Message: "Login failed",
				Error:   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Login successful",
		Data: map[string]interface{}{
			"loginTime": h.bankService.GetLoginTime(),
			"isLoggedIn": h.bankService.IsLoggedIn(),
		},
	})
}

// GetLoginStatus handles GET /api/transfer/login/status
func (h *TransferHandler) GetLoginStatus(c *gin.Context) {
	isLoggedIn := h.bankService.IsLoggedIn()
	loginTime := h.bankService.GetLoginTime()

	c.JSON(http.StatusOK, model.APIResponse{
		Success: true,
		Message: "Login status retrieved",
		Data: map[string]interface{}{
			"isLoggedIn": isLoggedIn,
			"loginTime":  loginTime,
			"sessionAge": h.bankService.GetSessionAge(),
		},
	})
}
