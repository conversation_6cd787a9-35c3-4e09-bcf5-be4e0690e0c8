package config

import (
	"time"

	"github.com/spf13/viper"
)

type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	Redis      RedisConfig      `mapstructure:"redis"`
	NamABank   NamABankConfig   `mapstructure:"namabank"`
	HTTPClient HTTPClientConfig `mapstructure:"httpclient"`
	APIKey     string           `mapstructure:"apikey"`
	LogLevel   string           `mapstructure:"log_level"`
}

type ServerConfig struct {
	Port int `mapstructure:"port"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type NamABankConfig struct {
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
	DebitAccount string `mapstructure:"debit_account"`
	BaseURL      string `mapstructure:"base_url"`
	OPSURL       string `mapstructure:"ops_url"`
}

type HTTPClientConfig struct {
	Timeout time.Duration `mapstructure:"timeout"`
}

func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// Set default values
	setDefaults()

	// Read environment variables
	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, err
		}
	}

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 8888)

	// Redis defaults
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.password", "")
	viper.SetDefault("redis.db", 0)

	// NamABank defaults
	viper.SetDefault("namabank.base_url", "https://ops-api.namabank.com.vn")
	viper.SetDefault("namabank.ops_url", "https://ops.namabank.com.vn")

	// HTTP Client defaults
	viper.SetDefault("httpclient.timeout", "30s")

	// Log level default
	viper.SetDefault("log_level", "info")

	// API Key default
	viper.SetDefault("apikey", "test-api-key-123")
}
