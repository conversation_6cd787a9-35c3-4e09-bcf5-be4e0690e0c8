package com.example.transfer.service.cronjob;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.concurrent.ScheduledFuture;

@Component
@Slf4j
public class TaskManager {
    private final TaskScheduler taskScheduler;
    private final HashMap<String, ScheduledFuture<?>> tasks = new HashMap<>();
    private final HashMap<String, TaskInfo> taskInfo = new HashMap<>();

    public TaskManager(TaskScheduler taskScheduler) {
        this.taskScheduler = taskScheduler;
    }

    public void startTask(String id, Runnable task, long fixedDelayInSeconds, long startTimeInSeconds) {
        log.info("Start task: {}", id);
        ScheduledFuture<?> future = taskScheduler.scheduleWithFixedDelay(
                task,
                Instant.now().plusSeconds(startTimeInSeconds),
                Duration.ofSeconds(fixedDelayInSeconds)
        );
        tasks.put(id, future);
        if (taskInfo.containsKey(id)) {
            TaskInfo task1 = taskInfo.get(id);
            task1.setIsRunning(true);
            task1.setFixedDelayInSeconds(fixedDelayInSeconds);
            task1.setStartTime(LocalDateTime.now().plusSeconds(startTimeInSeconds));
        } else {
            taskInfo.put(id, TaskInfo.builder()
                    .fixedDelayInSeconds(fixedDelayInSeconds)
                    .startTime(LocalDateTime.now().plusSeconds(startTimeInSeconds))
                    .isRunning(true)
                    .task(task)
                    .build()
            );
        }
    }

    public void stopTask(String id) {
        ScheduledFuture<?> future = tasks.get(id);
        if (future != null) {
            log.info("Stop task: {}", id);
            future.cancel(true);
            tasks.remove(id);
            taskInfo.get(id).setIsRunning(false);
        }
    }

    public void restartTask(String id, long fixedDelayInSeconds, long startTimeInSeconds) {
        log.info("Restart task: {}", id);
        stopTask(id);
        startTask(id, taskInfo.get(id).task, fixedDelayInSeconds, startTimeInSeconds);
    }

    public HashMap<String, TaskInfo> getTaskInfo() {
        return taskInfo;
    }

    @Getter
    @Setter
    @Builder
    public static class TaskInfo {
        private Long fixedDelayInSeconds;
        private Boolean isRunning;
        private LocalDateTime startTime;
        private Runnable task;
    }
}
