package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"transfer-service/internal/config"
	"transfer-service/internal/handler"
	"transfer-service/internal/service"
	"transfer-service/pkg/redis"
	"transfer-service/pkg/httpclient"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logrus.Fatalf("Failed to load config: %v", err)
	}

	// Setup logger
	setupLogger(cfg.LogLevel)

	// Initialize Redis client
	redisClient, err := redis.NewClient(cfg.Redis)
	if err != nil {
		logrus.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()

	// Initialize HTTP client
	httpClient := httpclient.New(cfg.HTTPClient)

	// Initialize services
	queueService := service.NewQueueService(redisClient)
	bankService := service.NewBankService(httpClient, cfg.NamABank)
	transferService := service.NewTransferService(queueService, bankService)

	// Initialize handlers
	transferHandler := handler.NewTransferHandler(transferService, cfg.APIKey)
	wsHandler := handler.NewWebSocketHandler(bankService)

	// Setup router
	router := setupRouter(transferHandler, wsHandler)

	// Start server
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Server.Port),
		Handler: router,
	}

	// Start server in a goroutine
	go func() {
		logrus.Infof("Starting server on port %d", cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	logrus.Info("Shutting down server...")

	// Graceful shutdown with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logrus.Fatalf("Server forced to shutdown: %v", err)
	}

	logrus.Info("Server exited")
}

func setupLogger(level string) {
	logrus.SetFormatter(&logrus.JSONFormatter{})
	logrus.SetOutput(os.Stdout)

	switch level {
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "info":
		logrus.SetLevel(logrus.InfoLevel)
	case "warn":
		logrus.SetLevel(logrus.WarnLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}
}

func setupRouter(transferHandler *handler.TransferHandler, wsHandler *handler.WebSocketHandler) *gin.Engine {
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()
	
	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// API routes
	api := router.Group("/api")
	{
		transfer := api.Group("/transfer")
		{
			transfer.POST("", transferHandler.Transfer)
			transfer.GET("/test", transferHandler.Test)
			transfer.GET("/queue/status", transferHandler.GetQueueStatus)
			transfer.GET("/queue", transferHandler.GetQueuedTransfers)
			transfer.POST("/queue/process", transferHandler.ProcessQueue)
			transfer.DELETE("/queue", transferHandler.ClearQueue)
		}
	}

	// WebSocket routes
	router.GET("/ws/otp", wsHandler.HandleOTP)
	router.GET("/ws/namabank", wsHandler.HandleNamAWebSocket)

	return router
}
