package httpclient

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"transfer-service/internal/config"

	"github.com/sirupsen/logrus"
)

type Client struct {
	httpClient *http.Client
	timeout    time.Duration
}

type RequestOptions struct {
	Headers map[string]string
	Timeout time.Duration
}

// Response wraps http.Response for convenience
type Response struct {
	*http.Response
}

func New(cfg config.HTTPClientConfig) *Client {
	return &Client{
		httpClient: &http.Client{
			Timeout: cfg.Timeout,
		},
		timeout: cfg.Timeout,
	}
}

func (c *Client) Get(url string, options *RequestOptions) (*http.Response, error) {
	return c.doRequest("GET", url, nil, options)
}

func (c *Client) Post(url string, body interface{}, options *RequestOptions) (*http.Response, error) {
	return c.doRequest("POST", url, body, options)
}

func (c *Client) Put(url string, body interface{}, options *RequestOptions) (*http.Response, error) {
	return c.doRequest("PUT", url, body, options)
}

func (c *Client) Delete(url string, options *RequestOptions) (*http.Response, error) {
	return c.doRequest("DELETE", url, nil, options)
}

// PostJSON sends a POST request with JSON body and returns response body as bytes
func (c *Client) PostJSON(url string, data interface{}, headers map[string]string) ([]byte, error) {
	options := &RequestOptions{
		Headers: headers,
	}

	resp, err := c.Post(url, data, options)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return body, nil
}



func (c *Client) doRequest(method, url string, body interface{}, options *RequestOptions) (*http.Response, error) {
	var reqBody io.Reader

	if body != nil {
		jsonData, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		reqBody = bytes.NewBuffer(jsonData)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set default headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br, zstd")
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
	req.Header.Set("Accept-Language", "vi,en-US;q=0.9,en;q=0.8")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("DNT", "1")

	// Set custom headers if provided
	if options != nil && options.Headers != nil {
		for key, value := range options.Headers {
			req.Header.Set(key, value)
		}
	}

	// Use custom timeout if provided
	client := c.httpClient
	if options != nil && options.Timeout > 0 {
		client = &http.Client{Timeout: options.Timeout}
	}

	logrus.Debugf("Making %s request to %s", method, url)
	
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}

	logrus.Debugf("Response status: %d", resp.StatusCode)
	
	return resp, nil
}

func (c *Client) ReadResponseBody(resp *http.Response) ([]byte, error) {
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return body, nil
}

func (c *Client) DecodeJSONResponse(resp *http.Response, target interface{}) error {
	defer resp.Body.Close()
	
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(target); err != nil {
		return fmt.Errorf("failed to decode JSON response: %w", err)
	}

	return nil
}
