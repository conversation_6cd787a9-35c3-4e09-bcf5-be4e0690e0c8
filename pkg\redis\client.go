package redis

import (
	"context"
	"fmt"
	"time"

	"transfer-service/internal/config"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
)

type Client struct {
	client *redis.Client
	ctx    context.Context
}

func NewClient(cfg config.RedisConfig) (*Client, error) {
	rdb := redis.NewClient(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password: cfg.Password,
		DB:       cfg.DB,
	})

	ctx := context.Background()

	// Test connection
	_, err := rdb.Ping(ctx).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	logrus.Info("Connected to Redis successfully")

	return &Client{
		client: rdb,
		ctx:    ctx,
	}, nil
}

func (c *Client) Close() error {
	return c.client.Close()
}

// List operations for queue functionality
func (c *Client) LPush(key string, values ...interface{}) error {
	return c.client.LPush(c.ctx, key, values...).Err()
}

func (c *Client) RPush(key string, values ...interface{}) error {
	return c.client.RPush(c.ctx, key, values...).Err()
}

func (c *Client) LPop(key string) (string, error) {
	return c.client.LPop(c.ctx, key).Result()
}

func (c *Client) RPop(key string) (string, error) {
	return c.client.RPop(c.ctx, key).Result()
}

func (c *Client) LLen(key string) (int64, error) {
	return c.client.LLen(c.ctx, key).Result()
}

func (c *Client) LRange(key string, start, stop int64) ([]string, error) {
	return c.client.LRange(c.ctx, key, start, stop).Result()
}

// Key operations
func (c *Client) Set(key string, value interface{}, expiration time.Duration) error {
	return c.client.Set(c.ctx, key, value, expiration).Err()
}

func (c *Client) Get(key string) (string, error) {
	return c.client.Get(c.ctx, key).Result()
}

func (c *Client) Del(keys ...string) error {
	return c.client.Del(c.ctx, keys...).Err()
}

func (c *Client) Exists(keys ...string) (int64, error) {
	return c.client.Exists(c.ctx, keys...).Result()
}

func (c *Client) Expire(key string, expiration time.Duration) error {
	return c.client.Expire(c.ctx, key, expiration).Err()
}

// Hash operations
func (c *Client) HSet(key string, values ...interface{}) error {
	return c.client.HSet(c.ctx, key, values...).Err()
}

func (c *Client) HGet(key, field string) (string, error) {
	return c.client.HGet(c.ctx, key, field).Result()
}

func (c *Client) HGetAll(key string) (map[string]string, error) {
	return c.client.HGetAll(c.ctx, key).Result()
}

func (c *Client) HDel(key string, fields ...string) error {
	return c.client.HDel(c.ctx, key, fields...).Err()
}
