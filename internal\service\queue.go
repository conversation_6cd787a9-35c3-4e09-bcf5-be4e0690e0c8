package service

import (
	"encoding/json"
	"fmt"

	"transfer-service/internal/model"
	"transfer-service/pkg/redis"

	"github.com/sirupsen/logrus"
)

const (
	TransferQueueKey = "deque_transfer"
)

type QueueService struct {
	redis redis.RedisClient
}

func NewQueueService(redisClient redis.RedisClient) *QueueService {
	return &QueueService{
		redis: redisClient,
	}
}

// Offer adds a transfer request to the end of the queue (right push)
func (s *QueueService) Offer(request *model.TransferRequest) error {
	data, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal transfer request: %w", err)
	}

	logrus.Infof("Adding transfer request to queue: %+v", request)
	
	err = s.redis.RPush(TransferQueueKey, string(data))
	if err != nil {
		return fmt.Errorf("failed to add transfer request to queue: %w", err)
	}

	return nil
}

// Poll removes and returns the first transfer request from the queue (left pop)
func (s *QueueService) Poll() (*model.TransferRequest, error) {
	data, err := s.redis.LPop(TransferQueueKey)
	if err != nil {
		if err.Error() == "redis: nil" {
			return nil, nil // Queue is empty
		}
		return nil, fmt.Errorf("failed to poll transfer request from queue: %w", err)
	}

	var request model.TransferRequest
	err = json.Unmarshal([]byte(data), &request)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal transfer request: %w", err)
	}

	return &request, nil
}

// OfferFirst adds a transfer request to the beginning of the queue (left push)
func (s *QueueService) OfferFirst(request *model.TransferRequest) error {
	data, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("failed to marshal transfer request: %w", err)
	}

	logrus.Infof("Adding transfer request to front of queue: %+v", request)
	
	err = s.redis.LPush(TransferQueueKey, string(data))
	if err != nil {
		return fmt.Errorf("failed to add transfer request to front of queue: %w", err)
	}

	return nil
}

// GetQueueSize returns the current size of the queue
func (s *QueueService) GetQueueSize() (int64, error) {
	size, err := s.redis.LLen(TransferQueueKey)
	if err != nil {
		return 0, fmt.Errorf("failed to get queue size: %w", err)
	}

	return size, nil
}

// GetAll returns all transfer requests in the queue without removing them
func (s *QueueService) GetAll() ([]*model.TransferRequest, error) {
	data, err := s.redis.LRange(TransferQueueKey, 0, -1)
	if err != nil {
		return nil, fmt.Errorf("failed to get all transfer requests: %w", err)
	}

	var requests []*model.TransferRequest
	for _, item := range data {
		var request model.TransferRequest
		err = json.Unmarshal([]byte(item), &request)
		if err != nil {
			logrus.Errorf("Failed to unmarshal transfer request: %v", err)
			continue
		}
		requests = append(requests, &request)
	}

	return requests, nil
}

// IsEmpty checks if the queue is empty
func (s *QueueService) IsEmpty() (bool, error) {
	size, err := s.GetQueueSize()
	if err != nil {
		return false, err
	}

	return size == 0, nil
}

// Clear removes all items from the queue
func (s *QueueService) Clear() error {
	err := s.redis.Del(TransferQueueKey)
	if err != nil {
		return fmt.Errorf("failed to clear queue: %w", err)
	}

	logrus.Info("Queue cleared successfully")
	return nil
}
