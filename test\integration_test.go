package test

import (
	"testing"

	"transfer-service/internal/config"
	"transfer-service/internal/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestTransferServiceIntegration(t *testing.T) {
	// Load test configuration
	cfg, err := config.Load()
	require.NoError(t, err)

	// Simple test to verify config loading
	assert.NotEmpty(t, cfg.APIKey)
	assert.NotZero(t, cfg.Server.Port)

	// Test model structures
	t.Run("Transfer Request Model", func(t *testing.T) {
		transferReq := model.TransferRequest{
			CrAccount: "**********",
			BankID:    "970407",
			Amount:    100000,
			Content:   "Test transfer",
		}

		assert.Equal(t, "**********", transferReq.CrAccount)
		assert.Equal(t, "970407", transferReq.BankID)
		assert.Equal(t, int64(100000), transferReq.Amount)
		assert.Equal(t, "Test transfer", transferReq.Content)
	})

	t.Run("API Response Model", func(t *testing.T) {
		response := model.APIResponse{
			Success: true,
			Message: "Test message",
			Data:    "test data",
		}

		assert.True(t, response.Success)
		assert.Equal(t, "Test message", response.Message)
		assert.Equal(t, "test data", response.Data)
	})
}
