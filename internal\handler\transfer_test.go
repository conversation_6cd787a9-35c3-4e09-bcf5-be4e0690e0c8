package handler

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"transfer-service/internal/model"
	"transfer-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockTransferService is a mock implementation of service.TransferService
type MockTransferService struct {
	mock.Mock
}

func (m *MockTransferService) QueueTransfer(request *model.TransferRequest) error {
	args := m.Called(request)
	return args.Error(0)
}

func (m *MockTransferService) ProcessTransfer(request *model.TransferRequest) error {
	args := m.Called(request)
	return args.Error(0)
}

func (m *MockTransferService) ProcessQueuedTransfers() error {
	args := m.Called()
	return args.Error(0)
}

func (m *MockTransferService) GetQueueStatus() (*model.QueueStatusResponse, error) {
	args := m.Called()
	return args.Get(0).(*model.QueueStatusResponse), args.Error(1)
}

func (m *MockTransferService) GetAllQueuedTransfers() ([]*model.TransferRequest, error) {
	args := m.Called()
	return args.Get(0).([]*model.TransferRequest), args.Error(1)
}

func (m *MockTransferService) ClearQueue() error {
	args := m.Called()
	return args.Error(0)
}

func setupTestRouter(handler *TransferHandler) *gin.Engine {
	gin.SetMode(gin.TestMode)
	router := gin.New()
	
	api := router.Group("/api")
	{
		transfer := api.Group("/transfer")
		{
			transfer.POST("", handler.Transfer)
			transfer.GET("/test", handler.Test)
			transfer.GET("/queue/status", handler.GetQueueStatus)
		}
	}
	
	return router
}

func TestTransferHandler_Transfer_Success(t *testing.T) {
	mockService := new(MockTransferService)
	handler := NewTransferHandler(mockService, "test-api-key")
	router := setupTestRouter(handler)

	transferRequest := model.TransferRequest{
		CrAccount: "**********",
		BankID:    "970407",
		Amount:    100000,
		Content:   "Test transfer",
	}

	mockService.On("QueueTransfer", &transferRequest).Return(nil)

	requestBody, _ := json.Marshal(transferRequest)
	req, _ := http.NewRequest("POST", "/api/transfer", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Api-Key", "test-api-key")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "Transfer request queued successfully", response.Message)

	mockService.AssertExpectations(t)
}

func TestTransferHandler_Transfer_InvalidAPIKey(t *testing.T) {
	mockService := new(MockTransferService)
	handler := NewTransferHandler(mockService, "test-api-key")
	router := setupTestRouter(handler)

	transferRequest := model.TransferRequest{
		CrAccount: "**********",
		BankID:    "970407",
		Amount:    100000,
		Content:   "Test transfer",
	}

	requestBody, _ := json.Marshal(transferRequest)
	req, _ := http.NewRequest("POST", "/api/transfer", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Api-Key", "invalid-key")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusUnauthorized, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
	assert.Equal(t, "Unauthorized", response.Message)
}

func TestTransferHandler_Transfer_InvalidRequest(t *testing.T) {
	mockService := new(MockTransferService)
	handler := NewTransferHandler(mockService, "test-api-key")
	router := setupTestRouter(handler)

	// Invalid request with missing required fields
	invalidRequest := map[string]interface{}{
		"crAccount": "",
		"amount":    -100,
	}

	requestBody, _ := json.Marshal(invalidRequest)
	req, _ := http.NewRequest("POST", "/api/transfer", bytes.NewBuffer(requestBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Api-Key", "test-api-key")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.False(t, response.Success)
}

func TestTransferHandler_Test(t *testing.T) {
	mockService := new(MockTransferService)
	handler := NewTransferHandler(mockService, "test-api-key")
	router := setupTestRouter(handler)

	req, _ := http.NewRequest("GET", "/api/transfer/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)
	assert.Equal(t, "Test endpoint working", response.Message)
	assert.Equal(t, "Test", response.Data)
}

func TestTransferHandler_GetQueueStatus(t *testing.T) {
	mockService := new(MockTransferService)
	handler := NewTransferHandler(mockService, "test-api-key")
	router := setupTestRouter(handler)

	expectedStatus := &model.QueueStatusResponse{
		QueueSize: 5,
		IsEmpty:   false,
	}

	mockService.On("GetQueueStatus").Return(expectedStatus, nil)

	req, _ := http.NewRequest("GET", "/api/transfer/queue/status", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response model.APIResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response.Success)

	// Convert response.Data to map for easier assertion
	statusData := response.Data.(map[string]interface{})
	assert.Equal(t, float64(5), statusData["queueSize"])
	assert.Equal(t, false, statusData["isEmpty"])

	mockService.AssertExpectations(t)
}
