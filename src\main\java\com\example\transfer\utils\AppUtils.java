package com.example.transfer.utils;

import okhttp3.Response;
import org.brotli.dec.BrotliInputStream;
import org.springframework.beans.factory.annotation.Value;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.DeflaterInputStream;
import java.util.zip.GZIPInputStream;

public class AppUtils {

    @Value("${PREFIX_PAYMENT:PP}")
    private String prefixPayment;
    @Value("${MUST_IN_CONTENT:mua steam}")
    private String mustInContent;


    public String getOrderIdFromContentPayment(String content) {
        String regex = prefixPayment.toLowerCase() + "[A-Za-z0-9]+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        if (content.contains(mustInContent.toLowerCase())) {
            if (matcher.find()) {
                return matcher.group();
            }
        }
        return null;
    }


    public static String renewCookie(List<String> newCookie, String cookie) {
        if (newCookie == null || newCookie.isEmpty()) return cookie;
        for (String newCookie1 : newCookie) {
            String[] cookieParts = newCookie1.split(";");
            String[] kv = cookieParts[0].trim().split("=");
            if (kv.length < 2) continue;
            cookie = cookie.replaceAll(kv[0] + "=[^;]*", kv[0] + "=" + kv[1]);
        }
        return cookie;
    }

    public static String formatCookie(String cookie) {
        if (cookie == null) return "";
        String[] keyValuePairs = cookie.split(";");
        Map<String, String> dataObject = new HashMap<>();
        for (String pair : keyValuePairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                dataObject.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        String csgoempire = dataObject.get("csgoempire");
        String doNotShare = dataObject.get("do_not_share_this_with_anyone_not_even_staff");

        return "csgoempire=" + csgoempire + ";do_not_share_this_with_anyone_not_even_staff=" + doNotShare;
    }

    public static InputStream getInputStreamBasedOnEncoding(Response response) throws IOException {
        if (response.body() == null) {
            throw new IOException("Response body is null");
        }
        InputStream inputStream = response.body().byteStream();
        String contentEncoding = response.header("Content-Encoding");

        if (contentEncoding == null) {
            return inputStream;
        }

        switch (contentEncoding) {
            case "br" -> {
                return new BrotliInputStream(inputStream);
            }
            case "gzip" -> {
                return new GZIPInputStream(inputStream);
            }
            case "deflate" -> {
                return new DeflaterInputStream(inputStream);
            }
            default -> {
                return inputStream;
            }
        }
    }

    public static String getResponse(Response response) throws IOException {
        InputStream inputStream = getInputStreamBasedOnEncoding(response);
        InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
        BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
        StringBuilder stringBuilder = new StringBuilder();
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            stringBuilder.append(line).append("\n");
        }
        return stringBuilder.toString();
    }


    @Value("${BOT_TOKEN}")
    private static String botToken;

    public static String getBotToken() {
        return botToken;
    }
}
