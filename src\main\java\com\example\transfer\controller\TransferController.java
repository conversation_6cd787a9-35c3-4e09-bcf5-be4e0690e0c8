package com.example.transfer.controller;

import com.example.transfer.dto.request.TransferRequest;
import com.example.transfer.exception.BadRequestException;
import com.example.transfer.service.AppService;
import com.example.transfer.service.DequeService;
import com.example.transfer.service.NamABankService;
import jakarta.annotation.security.PermitAll;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
@RestController
@RequestMapping("/api/transfer")
@RequiredArgsConstructor
@PermitAll
@Slf4j
public class TransferController {
    private final NamABankService namABankService;

    private final AppService appService;
    private final DequeService dequeService;

    //    @GetMapping
//    public void transfer(@RequestParam("otp") String otp) {
//        namABankService.otp(otp);
//    }
    @Value("${app.apikey}")
    private String apikey;

    @PostMapping
    public void transfer(@RequestBody TransferRequest transferRequest, @RequestHeader("X-Api-Key") String headerApiKey) {
        if (!apikey.equals(headerApiKey)) {
            throw new BadRequestException("Bad Request");
        }
        log.info("Transfer request: {}", transferRequest);
        dequeService.offer(transferRequest);
    }


    @GetMapping("/test")
    public String test() {
        System.out.println("Test");
        return "Test";
    }
}
