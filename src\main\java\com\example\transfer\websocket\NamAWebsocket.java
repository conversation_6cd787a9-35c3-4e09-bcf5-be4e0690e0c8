package com.example.transfer.websocket;

import com.example.transfer.config.AppContext;
import com.example.transfer.dto.Status;
import com.example.transfer.dto.request.TransferRequest;
import com.example.transfer.dto.request.WsNamARequest;
import com.example.transfer.event.BalanceChangedEvent;
import com.example.transfer.event.TransferErrorEvent;
import com.example.transfer.service.DequeService;
import com.example.transfer.service.NamABankService;
import com.example.transfer.service.cronjob.TaskId;
import com.example.transfer.service.cronjob.TaskManager;
import com.example.transfer.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@RequiredArgsConstructor
public class NamAWebsocket extends WebSocketListener {
    private final OkHttpClient client;
    private final NamABankService namABankService;
    private final JsonUtils jsonUtils;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher publisher;
    private final DequeService dequeService;
    private final TaskManager taskManager;
    private WebSocket ws;
    private String socketName;
    private String sign;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);


    public void connect(String socketName, String sign) {
        this.socketName = socketName;
        this.sign = sign;
        connect();
    }

    public synchronized void connect() {
        if (ws != null) {
            close();
        }

        log.info("Connecting to websocket");
        if (socketName == null || sign == null) {
            log.warn("Socket name or sign is missing. Skipping connect attempt.");
            return;
        }
        Request request = new Request.Builder().url("wss://ops-socket.namabank.com.vn/intelin").build();
        this.ws = client.newWebSocket(request, this);
        Map<String, String> data = Map.of("socketName", socketName, "sign", sign);
        ws.send(jsonUtils.objectToJsonString(data));
    }

    public void ping() {
        if (ws != null) {
            ws.send("ping");
        } else {
            log.warn("WebSocket is not connected");
        }
    }


    public synchronized void close() {
        if (ws != null) {
            log.info("Closing websocket");
            ws.close(1000, "Closing connection");
            ws = null;
        }
        socketName = null;
        sign = null;
        taskManager.stopTask(TaskId.PING);
        taskManager.stopTask(TaskId.FETCH_TRANSACTION);
    }

    public boolean isClosed() {
        return ws == null;
    }


    public void setSocketName(String socketName) {
        this.socketName = socketName;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        log.info("Connection opened");
    }

    @Override
    public void onMessage(WebSocket webSocket, String text) {
        if (!text.trim().startsWith("{")) {
            return;
        }
        try {
            log.info("Received message: {}", text);
            JsonNode data = objectMapper.readTree(text);
            int code = data.path("code").asInt(-1);
            String coll = data.path("coll").asText(null);
            if (coll != null) {
                webSocket.send(jsonUtils.objectToJsonString(WsNamARequest.builder().coll(coll).build()));
                log.info("Sent message: {}", coll);
            }
            if (code == 2400) {
                onGetInfoBankTransfer(data);
            } else if (code == 2098) {
                onGetRef(data);
            } else if (code == 4002) {
                onGetExpiredToken();
            } else if (code == 2420) {
                onGetBalanceChanged(data);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

    }

    @Override
    public void onMessage(WebSocket webSocket, ByteString bytes) {
        log.info("Received message hex: {} ", bytes.hex());
    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {
        log.info("Closing connection with code: {}, reason: {}", code, reason);
        webSocket.close(code, reason);
    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        t.printStackTrace();
    }

    public void onGetInfoBankTransfer(JsonNode data) {
        log.info("On get info bank transfer");
        JsonNode napasSuggestionNode = data.path("data").path("napasSuggestion");
        String creditName = napasSuggestionNode.path("recName").asText(null);

        if (creditName == null) {
            log.info("Credit name is null");
            publisher.publishEvent(new TransferErrorEvent("id", "Credit name is null")); //TODO: real id from another service
            AppContext.clear();
            return;
        }
        try {
            namABankService.checkBlackList(AppContext.getCreditAccount(), AppContext.getBankId());
            namABankService.transferFee(AppContext.getAmount());
            scheduler.schedule(() -> {
                namABankService.transferNapas(AppContext.getCreditAccount(), AppContext.getBankId(), creditName, AppContext.getAmount(), AppContext.getContent());
            }, 150, TimeUnit.MILLISECONDS);
        } catch (RuntimeException e) {
            log.warn("Error while transferring: {}", e.getMessage());
            publisher.publishEvent(new TransferErrorEvent("id", e.getMessage())); //TODO: real id from another service
            AppContext.clear();
        }
    }

    public void onGetRef(JsonNode data) {
        log.info("On get ref");
        String ref = data.path("data").path("otp").path("ref").asText(null);
        if (ref == null) {
            log.info("Ref is null");
            return;
        }
        log.info("Ref:::: {}", ref);
        AppContext.setRef(ref);
        AppContext.setStatus(Status.WAIT_OTP.ordinal());
    }

    public void onGetExpiredToken() {
        log.info("On get expired token");
        close();
        namABankService.processExpiredToken();
        if (AppContext.getStatus() != Status.WAITING.ordinal() && AppContext.getCreditAccount() != null) {
            dequeService.offerFirst(
                    TransferRequest.builder()
                            .crAccount(AppContext.getCreditAccount())
                            .amount(AppContext.getAmount())
                            .bankId(AppContext.getBankId())
                            .build()

            );
        }
        AppContext.clear();
    }

    public void onGetBalanceChanged(JsonNode data) {
        log.info("On get balance changed");
        if (data == null || !data.has("data")) {
            return;
        }
        JsonNode dataArray = data.get("data");
        if (!dataArray.isArray() || dataArray.size() == 0) {
            return;
        }
        for (JsonNode entry : dataArray) {
            if (entry.has("method") && "balanceChange".equals(entry.get("method").asText())) {
                if (entry.has("content")) {
                    String content = entry.get("content").asText();
                    Long balance = extractCurrentBalance(content);
                    if (balance != null) {
                        publisher.publishEvent(new BalanceChangedEvent(balance));
                    }
                    return;
                } else {
                    log.warn("Entry does not contain 'content' field");
                }
            }
        }
        log.warn("No entry with method 'balanceChange' found in data");
    }

    private Long extractCurrentBalance(String content) {
        try {
            int index = content.indexOf("Số dư hiện tại");
            if (index != -1) {
                String balancePart = content.substring(index);
                String[] words = balancePart.split(" ");
                if (words.length > 4) {
                    String balanceString = words[4].replace(".", "");
                    return Long.parseLong(balanceString);
                }
            }
        } catch (Exception e) {
            log.error("Error extracting balance from content", e);
        }
        return null;
    }
}
