package com.example.transfer.listener;

import com.example.transfer.event.BalanceChangedEvent;
import com.example.transfer.event.TransferErrorEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class AppListener {

    @EventListener
    @Async
    public void listenTransferError(TransferErrorEvent event) {
        log.error("Transfer error with id: {} ::: {}", event.id(), event.message());
    }

    @EventListener
    @Async
    public void listenBalanceChanged(BalanceChangedEvent event) {
        log.info("Balance changed: {}", event.balance());
    }

}
