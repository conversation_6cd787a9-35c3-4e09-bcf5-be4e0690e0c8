package service

import (
	"errors"
	"testing"

	"transfer-service/internal/model"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRedisClient is a mock implementation of redis.Client
type MockRedisClient struct {
	mock.Mock
}

func (m *MockRedisClient) RPush(key string, values ...interface{}) error {
	args := m.Called(key, values)
	return args.Error(0)
}

func (m *MockRedisClient) LPop(key string) (string, error) {
	args := m.Called(key)
	return args.String(0), args.Error(1)
}

func (m *MockRedisClient) LPush(key string, values ...interface{}) error {
	args := m.Called(key, values)
	return args.Error(0)
}

func (m *MockRedisClient) LLen(key string) (int64, error) {
	args := m.Called(key)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockRedisClient) LRange(key string, start, stop int64) ([]string, error) {
	args := m.Called(key, start, stop)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockRedisClient) Del(keys ...string) error {
	args := m.Called(keys)
	return args.Error(0)
}

func (m *MockRedisClient) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestQueueService_Offer(t *testing.T) {
	mockRedis := new(MockRedisClient)
	queueService := &QueueService{redis: mockRedis}

	request := &model.TransferRequest{
		CrAccount: "**********",
		BankID:    "970407",
		Amount:    100000,
		Content:   "Test transfer",
	}

	mockRedis.On("RPush", TransferQueueKey, mock.AnythingOfType("string")).Return(nil)

	err := queueService.Offer(request)

	assert.NoError(t, err)
	mockRedis.AssertExpectations(t)
}

func TestQueueService_Poll(t *testing.T) {
	mockRedis := new(MockRedisClient)
	queueService := &QueueService{redis: mockRedis}

	jsonData := `{"crAccount":"**********","bankId":"970407","amount":100000,"content":"Test transfer"}`
	mockRedis.On("LPop", TransferQueueKey).Return(jsonData, nil)

	request, err := queueService.Poll()

	assert.NoError(t, err)
	assert.NotNil(t, request)
	assert.Equal(t, "**********", request.CrAccount)
	assert.Equal(t, "970407", request.BankID)
	assert.Equal(t, int64(100000), request.Amount)
	assert.Equal(t, "Test transfer", request.Content)
	mockRedis.AssertExpectations(t)
}

func TestQueueService_Poll_EmptyQueue(t *testing.T) {
	mockRedis := new(MockRedisClient)
	queueService := &QueueService{redis: mockRedis}

	mockRedis.On("LPop", TransferQueueKey).Return("", errors.New("redis: nil"))

	request, err := queueService.Poll()

	assert.NoError(t, err)
	assert.Nil(t, request)
	mockRedis.AssertExpectations(t)
}

func TestQueueService_GetQueueSize(t *testing.T) {
	mockRedis := new(MockRedisClient)
	queueService := &QueueService{redis: mockRedis}

	mockRedis.On("LLen", TransferQueueKey).Return(int64(5), nil)

	size, err := queueService.GetQueueSize()

	assert.NoError(t, err)
	assert.Equal(t, int64(5), size)
	mockRedis.AssertExpectations(t)
}

func TestQueueService_IsEmpty(t *testing.T) {
	mockRedis := new(MockRedisClient)
	queueService := &QueueService{redis: mockRedis}

	mockRedis.On("LLen", TransferQueueKey).Return(int64(0), nil)

	isEmpty, err := queueService.IsEmpty()

	assert.NoError(t, err)
	assert.True(t, isEmpty)
	mockRedis.AssertExpectations(t)
}

func TestQueueService_Clear(t *testing.T) {
	mockRedis := new(MockRedisClient)
	queueService := &QueueService{redis: mockRedis}

	mockRedis.On("Del", []string{TransferQueueKey}).Return(nil)

	err := queueService.Clear()

	assert.NoError(t, err)
	mockRedis.AssertExpectations(t)
}
