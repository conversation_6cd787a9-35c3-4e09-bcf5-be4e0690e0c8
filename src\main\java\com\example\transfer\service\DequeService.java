package com.example.transfer.service;

import com.example.transfer.dto.request.TransferRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class DequeService {
    private final RedisTemplate<String, TransferRequest> redisTemplate;
    private final String DEQUE_KEY = "deque_transfer";

    public DequeService(RedisTemplate<String, TransferRequest> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void offer(TransferRequest element) {
        log.info(element.toString());
        redisTemplate.opsForList().rightPush(DEQUE_KEY, element);
    }

    public TransferRequest poll() {
        return redisTemplate.opsForList().leftPop(DEQUE_KEY);
    }

    public void offerFirst(TransferRequest element) {
        redisTemplate.opsForList().leftPush(DEQUE_KEY, element);
    }

    public Long getDequeSize() {
        return redisTemplate.opsForList().size(DEQUE_KEY);
    }

    public List<TransferRequest> getAll() {
        return redisTemplate.opsForList().range(DEQUE_KEY, 0, -1);
    }

    public boolean isEmpty() {
        return getDequeSize() == 0;
    }


}
