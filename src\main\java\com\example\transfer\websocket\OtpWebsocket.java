package com.example.transfer.websocket;

import com.example.transfer.event.ReceivedOtpEvent;
import com.example.transfer.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class OtpWebsocket extends WebSocketListener {
    private final OkHttpClient client;
    private final JsonUtils jsonUtils;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher publisher;
    private WebSocket ws;
    private boolean isReconnecting = false;
    private static final int RECONNECT_DELAY_MS = 5000;

    @Value("${app.otp.apikey}")
    private String apiKey;

    public void connect() {
        log.info("Connecting to websocket otp");
        if (isReconnecting) {
            log.info("Already attempting to reconnect. Skipping new connect attempt.");
            return;
        }
        String url = "wss://stream.pushbullet.com/websocket/" + apiKey;
        Request request = new Request.Builder().url(url).build();
        this.ws = client.newWebSocket(request, this);
    }

    public void ping() {
        if (ws != null) {
            ws.send("ping");
        }
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        log.info("Connection opened");
        isReconnecting = false;
    }

    @Override
    public void onMessage(WebSocket webSocket, String text) {
        try {
            JsonNode jsonNode = objectMapper.readTree(text);
            JsonNode pushNode = jsonNode.get("push");
            if (pushNode != null && pushNode.has("notifications") && pushNode.get("notifications").size() > 0) {
                JsonNode notificationsNode = pushNode.get("notifications");
                JsonNode firstNotification = notificationsNode.get(0);

                String title = firstNotification.get("title").asText("");
                if ("NamABank".equals(title)) {
                    String message = firstNotification.get("body").asText();
                    if (message != null && message.contains("OTP")) {
                        String otp = extractOtp(message);
                        if (otp != null) {
                            publisher.publishEvent(new ReceivedOtpEvent(otp));
                        }
                    }
                }
            }
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse message: {}", text, e);
        }
    }

    @Override
    public void onMessage(WebSocket webSocket, ByteString bytes) {
        log.info("Received message hex: {} ", bytes.hex());
    }

    @Override
    public void onClosing(WebSocket webSocket, int code, String reason) {
        log.info("Closing connection with code: {}, reason: {}", code, reason);
        webSocket.close(code, reason);
        scheduleReconnect();
    }

    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        t.printStackTrace();
        scheduleReconnect();
    }

    private String extractOtp(String message) {
        if (message != null && !message.isEmpty()) {
            String[] parts = message.split(" ");
            for (String part : parts) {
                if (part.matches("\\d{4}")) {
                    return part;
                }
            }
        }
        return null;
    }

    private void scheduleReconnect() {
        if (isReconnecting) {
            return;
        }
        isReconnecting = true;

        new Thread(() -> {
            try {
                log.info("Attempting to reconnect in {} ms", RECONNECT_DELAY_MS);
                Thread.sleep(RECONNECT_DELAY_MS);
                connect();
            } catch (InterruptedException e) {
                log.error("Reconnect attempt interrupted", e);
                Thread.currentThread().interrupt();
            }
        }).start();
    }

}
