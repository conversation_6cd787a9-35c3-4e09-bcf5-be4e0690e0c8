package service

import (
	"fmt"
	"time"

	"transfer-service/internal/model"

	"github.com/sirupsen/logrus"
)

type TransferService struct {
	queueService *QueueService
	bankService  *BankService
}

func NewTransferService(queueService *QueueService, bankService *BankService) *TransferService {
	return &TransferService{
		queueService: queueService,
		bankService:  bankService,
	}
}

// ProcessTransfer handles the complete transfer process
func (s *TransferService) ProcessTransfer(request *model.TransferRequest) error {
	logrus.Infof("Processing transfer request: %+v", request)

	// Step 1: Get bank information
	err := s.bankService.GetInfoBankTransfer(request.CrAccount, request.BankID)
	if err != nil {
		return fmt.Errorf("failed to get bank info: %w", err)
	}

	// Step 2: Check blacklist
	time.Sleep(200 * time.Millisecond) // Small delay as in original Java code
	err = s.bankService.CheckBlackList(request.CrAccount, request.BankID)
	if err != nil {
		return fmt.Errorf("failed to check blacklist: %w", err)
	}

	// Step 3: Calculate transfer fee
	err = s.bankService.TransferFee(request.Amount)
	if err != nil {
		return fmt.Errorf("failed to calculate transfer fee: %w", err)
	}

	// Step 4: Execute NAPAS transfer
	time.Sleep(200 * time.Millisecond) // Small delay as in original Java code
	err = s.bankService.TransferNapas(
		request.CrAccount,
		request.BankID,
		"", // Credit name - would need to be obtained from bank info step
		request.Amount,
		request.Content,
	)
	if err != nil {
		return fmt.Errorf("failed to execute NAPAS transfer: %w", err)
	}

	logrus.Info("Transfer process completed successfully")
	return nil
}

// QueueTransfer adds a transfer request to the queue
func (s *TransferService) QueueTransfer(request *model.TransferRequest) error {
	return s.queueService.Offer(request)
}

// ProcessQueuedTransfers processes all transfers in the queue
func (s *TransferService) ProcessQueuedTransfers() error {
	for {
		request, err := s.queueService.Poll()
		if err != nil {
			return fmt.Errorf("failed to poll from queue: %w", err)
		}

		if request == nil {
			// Queue is empty
			break
		}

		err = s.ProcessTransfer(request)
		if err != nil {
			logrus.Errorf("Failed to process transfer: %v", err)
			// Put the request back at the front of the queue for retry
			if retryErr := s.queueService.OfferFirst(request); retryErr != nil {
				logrus.Errorf("Failed to requeue transfer: %v", retryErr)
			}
			return err
		}
	}

	return nil
}

// GetQueueStatus returns the current queue status
func (s *TransferService) GetQueueStatus() (*model.QueueStatusResponse, error) {
	size, err := s.queueService.GetQueueSize()
	if err != nil {
		return nil, err
	}

	isEmpty, err := s.queueService.IsEmpty()
	if err != nil {
		return nil, err
	}

	return &model.QueueStatusResponse{
		QueueSize: int(size),
		IsEmpty:   isEmpty,
	}, nil
}

// GetAllQueuedTransfers returns all transfers currently in the queue
func (s *TransferService) GetAllQueuedTransfers() ([]*model.TransferRequest, error) {
	return s.queueService.GetAll()
}

// ClearQueue removes all transfers from the queue
func (s *TransferService) ClearQueue() error {
	return s.queueService.Clear()
}
