package httpclient

import "net/http"

// HTTPClient interface for HTTP operations
type HTTPClient interface {
	Get(url string, options *RequestOptions) (*http.Response, error)
	Post(url string, body interface{}, options *RequestOptions) (*http.Response, error)
	Put(url string, body interface{}, options *RequestOptions) (*http.Response, error)
	Delete(url string, options *RequestOptions) (*http.Response, error)
	PostJSON(url string, data interface{}, headers map[string]string) ([]byte, error)
	ReadResponseBody(resp *http.Response) ([]byte, error)
}

// Ensure Client implements HTTPClient interface
var _ HTTPClient = (*Client)(nil)
