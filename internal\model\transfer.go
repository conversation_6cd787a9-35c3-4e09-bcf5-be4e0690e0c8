package model

import "time"

// TransferRequest represents the main transfer request
type TransferRequest struct {
	CrAccount string `json:"crAccount" validate:"required"`
	BankID    string `json:"bankId" validate:"required"`
	Amount    int64  `json:"amount" validate:"required,min=1"`
	Content   string `json:"content" validate:"required"`
}

// BankInfoTransferRequest for getting bank information
type BankInfoTransferRequest struct {
	Number       string `json:"number"`
	BankID       string `json:"bankId"`
	Type         int    `json:"type"`
	Channel      string `json:"channel"`
	DebitAccount string `json:"debitAccount"`
}

// BlackListRequest for checking blacklist
type BlackListRequest struct {
	BankCiID      string `json:"bankCiId"`
	AccountNumber string `json:"accountNumber"`
	ChannelID     string `json:"channelId"`
	IDRequest     string `json:"idRequest"`
}

// TransferFeeRequest for calculating transfer fee
type TransferFeeRequest struct {
	Amount          int64  `json:"amount"`
	DebitAccount    string `json:"debitAccount"`
	IDRequest       string `json:"idRequest"`
	Currency        string `json:"currency"`
	TransactionType string `json:"transactionType"`
}

// TransferNapasRequest for NAPAS transfer
type TransferNapasRequest struct {
	CreditAccount       string `json:"creditAccount"`
	BankCode           string `json:"bankCode"`
	CreditName         string `json:"creditName"`
	TransactionAmount  string `json:"transactionAmount"`
	TransactionCurrency string `json:"transactionCurrency"`
	TransactionDetail  string `json:"transactionDetail"`
	TypeDescription    int    `json:"typeDescription"`
	DebitAccount       string `json:"debitAccount"`
	FeKey              string `json:"feKey"`
	IDRequest          string `json:"idRequest"`
}

// OTPRequest for OTP verification
type OTPRequest struct {
	IDRequest string `json:"idRequest"`
	CodeOTP   string `json:"codeOTP"`
	Ref       string `json:"ref"`
}

// TransactionRequest for getting transaction history
type TransactionRequest struct {
	AccountNumber string   `json:"accountNumber"`
	FromDate      int64    `json:"fromDate"`
	ToDate        int64    `json:"toDate"`
	Page          int      `json:"page"`
	Size          int      `json:"size"`
	TxnTypes      []string `json:"txnTypes"`
}

// IDRequest response containing request ID
type IDRequest struct {
	IDRequest string `json:"idRequest"`
}

// NamAResponse generic response from NamA Bank
type NamAResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// SocketCredential for WebSocket authentication
type SocketCredential struct {
	SocketName string `json:"socketName"`
	Sign       string `json:"sign"`
}

// Status enum for transfer status
type Status int

const (
	StatusPending Status = iota
	StatusTransfer
	StatusCompleted
	StatusFailed
)

// LoginResponse from bank login
type LoginResponse struct {
	Code string `json:"code"`
	Data struct {
		Auth struct {
			DeviceID string `json:"deviceID"`
			Token    string `json:"token"`
		} `json:"auth"`
	} `json:"data"`
}

// TransferContext holds transfer session data
type TransferContext struct {
	DeviceID  string
	Token     string
	RequestID string
	Ref       string
	Content   string
	Status    Status
	CreatedAt time.Time
	UpdatedAt time.Time
}
