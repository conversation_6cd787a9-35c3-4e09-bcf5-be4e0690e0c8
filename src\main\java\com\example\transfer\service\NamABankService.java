package com.example.transfer.service;

import com.example.transfer.config.AppContext;
import com.example.transfer.dto.SocketCredential;
import com.example.transfer.dto.Status;
import com.example.transfer.dto.request.*;
import com.example.transfer.dto.response.NamAResponse;
import com.example.transfer.event.ReceivedOtpEvent;
import com.example.transfer.event.TransferErrorEvent;
import com.example.transfer.exception.BadRequestException;
import com.example.transfer.utils.AppUtils;
import com.example.transfer.utils.JsonUtils;
import com.example.transfer.utils.SecurityUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.*;
import com.microsoft.playwright.options.BoundingBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.RequestBody;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@Slf4j
@RequiredArgsConstructor
public class NamABankService {
    private final JsonUtils jsonUtils;
    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;
    private final DequeService dequeService;
    @Value("${app.namabank.username}")
    private String username;
    @Value("${app.namabank.password}")
    private String password;
    @Value("${app.namabank.debitAccount}")
    private String debitAccount;
    private String deviceId;
    private String token;
    private String requestId;
    private String socketName;
    private String sign;
    private final ApplicationEventPublisher publisher;
    private final AtomicBoolean isLogin = new AtomicBoolean(false);

    public Boolean getIsLogin() {
        return isLogin.get();
    }

    public void processExpiredToken() {
        log.info("Process expired token");
        this.deviceId = null;
        this.token = null;
        this.requestId = null;
        this.socketName = null;
        this.sign = null;
        isLogin.set(false);
    }

    public void processFrameSent(String text) {
        log.info("Frame sent: " + text);
        try {
            if (!text.trim().startsWith("{")) {
                return;
            }
            JsonNode data = objectMapper.readTree(text);
            String socketName = data.path("socketName").asText(null);
            String sign = data.path("sign").asText(null);

            if (socketName != null && sign != null) {
                this.socketName = socketName;
                this.sign = sign;
                log.info("Socket name: {}", socketName);
                log.info("Sign: {}", sign);
            }

        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void processFrameReceived(String text) {
        log.info("Frame received: " + text);
    }

    public SocketCredential login() {
        log.info("Start open chromium");
        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.firefox().launch();
            Page page = browser.newPage();
            log.info("Start login");
            page.onWebSocket(webSocket -> {
                log.info("WebSocket opened: " + webSocket.url());
                webSocket.onFrameSent(frame -> {
                    processFrameSent(frame.text());
                });
                webSocket.onFrameReceived(frame -> {
                    processFrameReceived(frame.text());
                });
                webSocket.onClose((f) -> {
                    log.info("WebSocket closed");
                });
            });
            page.navigate("https://ops.namabank.com.vn/public/view");
            log.info("Click login popup");
            Locator imageLocator = page.locator("img[usemap='#image_map'][src='./assets/images/cardView/vi/control.png']");
            BoundingBox boundingBox = imageLocator.boundingBox();
            double x = boundingBox.x + 203;
            double y = boundingBox.y + 200;
            page.mouse().click(x, y);
            log.info("Click login button:: ({},{})", x, y);
            page.waitForSelector("input[placeholder='Tên đăng nhập']", new Page.WaitForSelectorOptions().setTimeout(15000));
//            page.press("input[placeholder='Tên đăng nhập']", "Tab");
            page.getByPlaceholder("Tên đăng nhập").click();
            page.getByPlaceholder("Tên đăng nhập").focus();
            page.getByPlaceholder("Tên đăng nhập").fill(username);
            String filledValue = page.getByPlaceholder("Tên đăng nhập").inputValue();
            if (filledValue.equals(username)) {
                log.info("Username filled successfully: {}", filledValue);
            } else {
                log.warn("Username not filled correctly. Retrying...");
            }
            page.getByPlaceholder("Tên đăng nhập").press("Tab");
            page.getByPlaceholder("Mật khẩu").fill(password);
            log.info("Click login button");
            Response loginResponse = page.waitForResponse(
                    response -> response.url().contains("login"),
                    () -> page.locator("button[ng-click='handleConfirm()']").click()
            );
            log.info("request: {}", loginResponse.request().postData());
            log.info("Finish login");
            String loginResponseStr = new String(loginResponse.body());
            log.info("Login response: {}", loginResponseStr);

            JsonNode jsonNode = jsonUtils.stringToJsonNode(loginResponseStr);
            if (!jsonNode.get("code").asText().equals("2000")) {
                log.info("Login failed");
                browser.close();
                return new SocketCredential();
            }

            this.deviceId = jsonNode.get("data").get("auth").get("deviceID").asText();
            this.token = jsonNode.get("data").get("auth").get("token").asText();
            log.info("Device ID: {}", deviceId);
            log.info("Token: {}", token);

            page.waitForTimeout(1000);
            log.info("Close browser");
            isLogin.set(true);
            browser.close();
//            publisher.publishEvent(new LoginSuccessEvent(socketName, sign));
            return new SocketCredential(socketName, sign);
        } catch (Exception e) {
            log.error("Login: {}", e.getMessage(), e);
            return new SocketCredential();
        }
    }

    public void transfer(TransferRequest transferRequest) {
        try {
            getInfoBankTransfer(transferRequest.getCrAccount(), transferRequest.getBankId());
            AppContext.setContent(transferRequest.getContent());
        } catch (RuntimeException e) {
            log.warn("Error while getting info bank transfer: {}", e.getMessage());
            publisher.publishEvent(new TransferErrorEvent("id", e.getMessage())); //TODO: real id from another service
            AppContext.clear();
        }
    }

    private void transfer2() throws InterruptedException {
        getInfoBankTransfer("123444", "970407");
        Thread.sleep(200);
        checkBlackList("123444", "970407");
        transferFee(10000);
        Thread.sleep(200);
        transferNapas("123444", "970407", "BUI CONG MINH HOANG", 10000,"Abc");
    }

    public void getInfoBankTransfer(String accountNumber, String bankId) {
        log.info("Get info bank to transfer");
        if (!StringUtils.hasText(accountNumber) || !StringUtils.hasText(bankId)) {
            log.info("Account number or bank id is null");
            throw new BadRequestException("Account number or bank id is null");
        }
        RequestBody requestBody = RequestBody.create(
                jsonUtils.objectToJsonString(BankInfoTransferRequest.builder()
                        .number(accountNumber)
                        .bankId(bankId)
                        .type(0)
                        .channel("99")
                        .debitAccount(this.debitAccount)
                        .build()
                ), MediaType.get("application/json")
        );
        okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://ops-api.namabank.com.vn/user/information")
                .headers(buildHeaders())
                .post(requestBody)
                .build();
        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Get info bank to transfer: {} ::::: {}", response.code(), response.body() != null ? response.body().string() : null);
                throw new RuntimeException("Get info bank to transfer failed");
            }
            String responseStr = AppUtils.getResponse(response);
            log.info("getInfoBankTransfer::::Response: {}", responseStr);
            IdRequest idRequest = objectMapper.readValue(responseStr, IdRequest.class);
            this.requestId = idRequest.getIdRequest();
            AppContext.setIdRequest(requestId);
        } catch (IOException e) {
            log.error("Get info bank to transfer: {}", e.getMessage(), e);
            throw new RuntimeException("Get info bank to transfer failed");
        }
    }

    public void checkBlackList(String accountNumber, String bankId) {
        log.info("Check black list");
        if (!StringUtils.hasText(accountNumber) || !StringUtils.hasText(bankId)) {
            log.info("Account number or bank id is null");
            throw new BadRequestException("Account number or bank id is null");
        }
        if (requestId == null) {
            log.info("Request id is null");
            throw new BadRequestException("Request id is null");
        }
        RequestBody requestBody = RequestBody.create(
                jsonUtils.objectToJsonString(BlackListRequest.builder()
                        .bankCiId(bankId)
                        .accountNumber(accountNumber)
                        .channelId("NAPAS")
                        .idRequest(requestId).build()),
                MediaType.get("application/json")
        );
        okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://ops-api.namabank.com.vn/user/checkBlackList")
                .headers(buildHeaders())
                .post(requestBody)
                .build();
        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Check black list: {} ::::: {}", response.code(), response.body() != null ? response.body().string() : null);
                throw new RuntimeException("Check black list failed");
            }
            String responseStr = AppUtils.getResponse(response);
            log.info("checkBlackList::::Response: {}", responseStr);
        } catch (IOException e) {
            log.error("Check black list: {}", e.getMessage(), e);
            throw new RuntimeException("Check black list failed");
        }
    }

    public void transferFee(long amount) {
        log.info("Transfer fee");
        if (amount <= 0) {
            log.info("Amount is invalid");
            throw new BadRequestException("Amount is invalid");
        }
        if (requestId == null) {
            log.info("Request id is null");
            throw new BadRequestException("Request id is null");
        }
        RequestBody requestBody = RequestBody.create(
                jsonUtils.objectToJsonString(TransferFeeRequest.builder()
                        .amount(amount)
                        .debitAccount(debitAccount)
                        .idRequest(requestId)
                        .currency("VND")
                        .transactionType("NAPAS")
                        .build()
                ), MediaType.get("application/json")
        );
        okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://ops-api.namabank.com.vn/transfer/fee")
                .headers(buildHeaders())
                .post(requestBody)
                .build();
        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Transfer fee: {} ::::: {}", response.code(), response.body() != null ? response.body().string() : null);
                throw new RuntimeException("Transfer fee failed");
            }
            String responseStr = AppUtils.getResponse(response);
            log.info("transferFee::::Response: {}", responseStr);
        } catch (IOException e) {
            log.error("Transfer fee: {}", e.getMessage(), e);
            throw new RuntimeException("Transfer fee failed");
        }
    }

    public void transferNapas(String creditAccount, String bankId, String creditName, long amount , String content) {
        log.info("Transfer napas");
        if (requestId == null) {
            log.info("Request id is null");
            throw new BadRequestException("Request id is null");
        }
        if (amount <= 0) {
            log.info("Amount is invalid");
            throw new BadRequestException("Amount is invalid");
        }
        if (!StringUtils.hasText(creditAccount) || !StringUtils.hasText(bankId) || !StringUtils.hasText(creditName)) {
            log.info("Credit account, bank id or credit name is null");
            throw new BadRequestException("Credit account, bank id or credit name is null");
        }
        RequestBody requestBody = RequestBody.create(
                jsonUtils.objectToJsonString(TransferNapasRequest.builder()
                        .creditAccount(creditAccount)
                        .bankCode(bankId)
                        .creditName(creditName)
                        .transactionAmount(String.valueOf(amount))
                        .transactionCurrency("VND")
                        .transactionDetail(content)
                        .typeDescription(0)
                        .debitAccount(debitAccount)
                        .feKey(SecurityUtils.generateChecking())
                        .idRequest(requestId)
                        .build()
                ), MediaType.get("application/json")
        );
        okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://ops-api.namabank.com.vn/transfer/napas")
                .headers(buildHeaders())
                .post(requestBody)
                .build();
        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Transfer napas: {} ::::: {}", response.code(), response.body() != null ? response.body().string() : null);
                throw new RuntimeException("Transfer napas failed");
            }
            String responseStr = AppUtils.getResponse(response);
            log.info("transferNapas::::Response: {}", responseStr);
            AppContext.setStatus(Status.TRANSFER.ordinal());
        } catch (IOException e) {
            log.error("Transfer napas: {}", e.getMessage(), e);
            throw new RuntimeException("Transfer napas failed");
        }
    }

    public void otp(String otp) throws InterruptedException {
        log.info("OTP");
        if (requestId == null) {
            log.info("Request id is null");
            throw new BadRequestException("Request id is null");
        }
        if (!StringUtils.hasText(deviceId) || !StringUtils.hasText(token)) {
            log.info("Device id or token is null");
            throw new BadRequestException("Device id or token is null");
        }
        if (!StringUtils.hasText(otp)) {
            log.info("OTP is null");
            throw new BadRequestException("OTP is null");
        }
        if (!StringUtils.hasText(AppContext.getRef())) {
            log.info("Ref is null");
            Thread.sleep(1000);
            if (!StringUtils.hasText(AppContext.getRef())) {
                log.info("Ref is null");
                throw new BadRequestException("Ref is null");
            }
        }

        RequestBody requestBody = RequestBody.create(
                jsonUtils.objectToJsonString(OtpRequest.builder()
                        .idRequest(requestId)
                        .codeOTP(otp)
                        .ref(AppContext.getRef())
                        .build()
                ), MediaType.get("application/json")
        );
        okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://ops-api.namabank.com.vn/auth/otp")
                .headers(buildHeaders())
                .post(requestBody)
                .build();

        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("OTP: {} ::::: {}", response.code(), response.body() != null ? response.body().string() : null);
                throw new RuntimeException("OTP failed");
            }
            String responseStr = AppUtils.getResponse(response);
            log.info("otp::::Response: {}", responseStr);
            NamAResponse namAResponse = objectMapper.readValue(responseStr, NamAResponse.class);
            if (namAResponse.getCode() == 2099) {
                log.info("OTP success");
                this.requestId = null;
                AppContext.clear();
                AppContext.setCurrentTime();
            }
            if (namAResponse.getCode() == 4031) {
                throw new RuntimeException("OTP failed");
            }
        } catch (IOException e) {
            log.error("OTP: {}", e.getMessage(), e);
            throw new RuntimeException("OTP failed");
        }
    }

    @EventListener
    @Async
    public void onReceivedOtpEvent(ReceivedOtpEvent event) {
        try {
            log.info("Received OTP event: {}", event.otp());
            otp(event.otp());
        } catch (RuntimeException | InterruptedException e) {
            log.warn("Error while enter otp: {}", e.getMessage());
            publisher.publishEvent(new TransferErrorEvent("id", e.getMessage())); //TODO: real id from another service
            AppContext.clear();
        }
    }

    public void getTransactions() {
        log.info("Get transactions");
        Instant now = Instant.now();
        Instant fifteenDaysAgo = now.minus(Duration.ofDays(15));
        long fromDate = fifteenDaysAgo.toEpochMilli();
        long toDate = now.toEpochMilli();
        RequestBody requestBody = RequestBody.create(
                jsonUtils.objectToJsonString(TransactionRequest.builder()
                        .accountNumber(debitAccount)
                        .fromDate(fromDate)
                        .toDate(toDate)
                        .page(1)
                        .size(100)
                        .txnTypes(new ArrayList<>())
                        .build()
                ), MediaType.get("application/json")
        );


        okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://ops-api.namabank.com.vn/user/transactions")
                .headers(buildHeaders())
                .post(requestBody)
                .build();
        try (okhttp3.Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("Get transactions: {} ::::: {}", response.code(), response.body() != null ? response.body().string() : null);
                throw new RuntimeException("Get transactions failed");
            }
            String responseStr = AppUtils.getResponse(response);
            log.info("getTransactions::::Response: {}", responseStr);
        } catch (IOException e) {
            log.error("Get transactions: {}", e.getMessage(), e);
            throw new RuntimeException("Get transactions failed");
        }
    }


    public Headers buildHeaders() {
        if (token == null || deviceId == null) {
            log.info("Token or device id is null");
            throw new RuntimeException("Token or device id is null");
        }
        return new Headers.Builder()
                .add("token", token)
                .add("device-id", deviceId)
                .add("checking", SecurityUtils.generateChecking())
                .add("Content-Type", "application/json")
                .add("Accept", "application/json, text/plain, */*")
                .add("Accept-Encoding", "gzip, deflate, br, zstd")
                .add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36")
                .add("Accept-Language", "vi,en-US;q=0.9,en;q=0.8")
                .add("Connection", "keep-alive")
                .add("DNT", "1")
                .add("Origin", "https://ops.namabank.com.vn")
                .add("Referer", "https://ops.namabank.com.vn/")
                .add("Host", "ops-api.namabank.com.vn")
                .add("Sec-Fetch-Dest", "empty")
                .add("Sec-Fetch-Mode", "cors")
                .add("Sec-Fetch-Site", "same-site")
                .add("sec-ch-ua", "\"Chromium\";v=\"130\", \"Google Chrome\";v=\"130\", \"Not?A_Brand\";v=\"99\"")
                .add("sec-ch-ua-mobile", "?0")
                .add("sec-ch-ua-platform", "\"Windows\"")
                .build();
    }
}
