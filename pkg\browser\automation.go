package browser

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"transfer-service/internal/config"
	"transfer-service/internal/model"

	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/chromedp"
	"github.com/sirupsen/logrus"
)

type AutomationService struct {
	config config.NamABankConfig
}

func NewAutomationService(config config.NamABankConfig) *AutomationService {
	return &AutomationService{
		config: config,
	}
}

// <PERSON><PERSON> performs automated login to Nam A Bank
func (s *AutomationService) Login() (*model.SocketCredential, error) {
	logrus.Info("Starting automated browser login")

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Create Chrome instance
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", false), // Set to true for headless mode
		chromedp.Flag("disable-gpu", false),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("disable-extensions", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.WindowSize(1920, 1080),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(ctx, opts...)
	defer cancel()

	// Create browser context
	browserCtx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(logrus.Debugf))
	defer cancel()

	var loginResponse string
	var socketCredentials model.SocketCredential

	err := chromedp.Run(browserCtx,
		// Navigate to the login page
		chromedp.Navigate(s.config.OPSURL+"/public/view"),
		chromedp.WaitVisible(`img[usemap='#image_map'][src='./assets/images/cardView/vi/control.png']`, chromedp.ByQuery),

		// Click on the login area (coordinates from Java code)
		chromedp.ActionFunc(func(ctx context.Context) error {
			logrus.Info("Clicking login area")
			return chromedp.Click(`img[usemap='#image_map'][src='./assets/images/cardView/vi/control.png']`, chromedp.ByQuery).Do(ctx)
		}),

		// Wait for login form to appear
		chromedp.WaitVisible(`input[placeholder='Tên đăng nhập']`, chromedp.ByQuery),

		// Fill username
		chromedp.ActionFunc(func(ctx context.Context) error {
			logrus.Info("Filling username")
			return chromedp.SendKeys(`input[placeholder='Tên đăng nhập']`, s.config.Username, chromedp.ByQuery).Do(ctx)
		}),

		// Fill password
		chromedp.ActionFunc(func(ctx context.Context) error {
			logrus.Info("Filling password")
			return chromedp.SendKeys(`input[placeholder='Mật khẩu']`, s.config.Password, chromedp.ByQuery).Do(ctx)
		}),

		// Set up network listener for login response
		chromedp.ActionFunc(func(ctx context.Context) error {
			logrus.Info("Setting up network listener")
			chromedp.ListenTarget(ctx, func(ev interface{}) {
				switch ev := ev.(type) {
				case *network.EventResponseReceived:
					if strings.Contains(ev.Response.URL, "login") || strings.Contains(ev.Response.URL, "auth") {
						logrus.Debugf("Network response: %s", ev.Response.URL)
					}
				}
			})
			return nil
		}),

		// Click login button and wait for response
		chromedp.ActionFunc(func(ctx context.Context) error {
			logrus.Info("Clicking login button")
			return chromedp.Click(`button[ng-click='handleConfirm()']`, chromedp.ByQuery).Do(ctx)
		}),

		// Wait for navigation or response
		chromedp.Sleep(3*time.Second),

		// Try to extract login response from page
		chromedp.ActionFunc(func(ctx context.Context) error {
			logrus.Info("Extracting login response")
			// This is a simplified approach - in a real implementation,
			// you would need to intercept the actual network response
			return nil
		}),
	)

	if err != nil {
		logrus.Errorf("Browser automation failed: %v", err)
		return nil, fmt.Errorf("browser automation failed: %w", err)
	}

	// Parse login response (simplified)
	if loginResponse != "" {
		var response model.LoginResponse
		if err := json.Unmarshal([]byte(loginResponse), &response); err != nil {
			return nil, fmt.Errorf("failed to parse login response: %w", err)
		}

		if response.Code != "2000" {
			return nil, fmt.Errorf("login failed with code: %s", response.Code)
		}

		logrus.Infof("Login successful - Device ID: %s", response.Data.Auth.DeviceID)
		// In a real implementation, you would also extract socket credentials
	}

	logrus.Info("Browser automation completed")
	return &socketCredentials, nil
}

// LoginWithWebSocketCapture performs login and captures WebSocket credentials
func (s *AutomationService) LoginWithWebSocketCapture() (*model.SocketCredential, *model.LoginResponse, error) {
	logrus.Info("Starting automated login with WebSocket capture")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", false),
		chromedp.Flag("disable-web-security", true),
		chromedp.Flag("disable-features", "VizDisplayCompositor"),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(ctx, opts...)
	defer cancel()

	browserCtx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	var socketCredentials model.SocketCredential
	var loginResponse model.LoginResponse
	var capturedResponse string

	err := chromedp.Run(browserCtx,
		// Enable network domain
		network.Enable(),

		// Set up listeners for WebSocket and network events
		chromedp.ActionFunc(func(ctx context.Context) error {
			chromedp.ListenTarget(ctx, func(ev interface{}) {
				switch ev := ev.(type) {
				case *network.EventWebSocketCreated:
					logrus.Infof("WebSocket created: %s", ev.URL)

				case *network.EventWebSocketFrameSent:
					logrus.Infof("WebSocket frame sent: %s", ev.Response.PayloadData)
					// Try to extract socket credentials from sent frames
					s.extractSocketCredentials(ev.Response.PayloadData, &socketCredentials)

				case *network.EventWebSocketFrameReceived:
					logrus.Infof("WebSocket frame received: %s", ev.Response.PayloadData)

				case *network.EventResponseReceived:
					if strings.Contains(ev.Response.URL, "login") || strings.Contains(ev.Response.URL, "auth") {
						logrus.Debugf("HTTP Response: %s - %d", ev.Response.URL, ev.Response.Status)
					}
				}
			})
			return nil
		}),

		// Navigate and perform login
		chromedp.Navigate(s.config.OPSURL+"/public/view"),
		chromedp.WaitVisible(`img[usemap='#image_map']`, chromedp.ByQuery),
		chromedp.Click(`img[usemap='#image_map']`, chromedp.ByQuery),
		chromedp.WaitVisible(`input[placeholder='Tên đăng nhập']`, chromedp.ByQuery),
		chromedp.SendKeys(`input[placeholder='Tên đăng nhập']`, s.config.Username, chromedp.ByQuery),
		chromedp.SendKeys(`input[placeholder='Mật khẩu']`, s.config.Password, chromedp.ByQuery),
		chromedp.Click(`button[ng-click='handleConfirm()']`, chromedp.ByQuery),

		// Wait for login to complete
		chromedp.Sleep(5*time.Second),

		// Try to capture the login response
		chromedp.ActionFunc(func(ctx context.Context) error {
			// This would need to be implemented to actually capture the response
			// For now, we'll simulate a successful response
			capturedResponse = `{"code":"2000","data":{"auth":{"deviceID":"test-device","token":"test-token"}}}`
			return nil
		}),
	)

	if err != nil {
		return nil, nil, fmt.Errorf("browser automation with WebSocket capture failed: %w", err)
	}

	// Parse captured response
	if capturedResponse != "" {
		if err := json.Unmarshal([]byte(capturedResponse), &loginResponse); err != nil {
			return nil, nil, fmt.Errorf("failed to parse login response: %w", err)
		}
	}

	return &socketCredentials, &loginResponse, nil
}

func (s *AutomationService) extractSocketCredentials(payload string, credentials *model.SocketCredential) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &data); err != nil {
		return
	}

	if socketName, exists := data["socketName"].(string); exists {
		credentials.SocketName = socketName
	}

	if sign, exists := data["sign"].(string); exists {
		credentials.Sign = sign
	}

	if credentials.SocketName != "" && credentials.Sign != "" {
		logrus.Infof("Socket credentials extracted - Socket Name: %s, Sign: %s",
			credentials.SocketName, credentials.Sign)
	}
}
