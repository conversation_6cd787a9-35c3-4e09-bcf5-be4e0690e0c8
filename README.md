# Transfer Service - Go Microservice

A Go microservice for handling bank transfers, converted from Java Spring Boot application. This service integrates with Nam A Bank API to process transfer requests through a queue-based system.

## Features

- **REST API**: HTTP endpoints for transfer operations
- **Queue Management**: Redis-based queue for transfer requests
- **Bank Integration**: Integration with Nam A Bank API
- **WebSocket Support**: Real-time OTP handling
- **Browser Automation**: Automated login using Chromedp
- **Containerized**: Docker and Docker Compose support

## Architecture

```
transfer-service/
├── cmd/server/          # Application entry point
├── internal/
│   ├── config/         # Configuration management
│   ├── handler/        # HTTP and WebSocket handlers
│   ├── service/        # Business logic services
│   ├── model/          # Data models and DTOs
│   └── middleware/     # HTTP middleware
├── pkg/
│   ├── redis/          # Redis client wrapper
│   ├── httpclient/     # HTTP client wrapper
│   └── browser/        # Browser automation
├── configs/            # Configuration files
└── docs/              # Documentation
```

## Technology Stack

- **Go 1.21**: Programming language
- **Gin**: Web framework
- **Redis**: Queue and caching
- **Chromedp**: Browser automation
- **Viper**: Configuration management
- **Logrus**: Structured logging
- **Docker**: Containerization

## Quick Start

### Prerequisites

- Go 1.21+
- Redis
- Docker (optional)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd transfer-service
   ```

2. **Install dependencies**
   ```bash
   make deps
   ```

3. **Configure the application**
   ```bash
   cp configs/config.yaml.example configs/config.yaml
   # Edit configs/config.yaml with your settings
   ```

4. **Run Redis**
   ```bash
   docker run -d -p 6379:6379 redis:7-alpine
   ```

5. **Run the application**
   ```bash
   make run
   ```

### Docker Deployment

1. **Using Docker Compose (Recommended)**
   ```bash
   docker-compose up -d
   ```

2. **Build and run manually**
   ```bash
   make docker-build
   make docker-run
   ```

## API Documentation

### Transfer Endpoints

#### POST /api/transfer
Queue a new transfer request.

**Headers:**
- `X-Api-Key`: API key for authentication
- `Content-Type`: application/json

**Request Body:**
```json
{
  "crAccount": "**********",
  "bankId": "970407",
  "amount": 100000,
  "content": "Transfer description"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Transfer request queued successfully",
  "data": {
    "transferId": "",
    "status": "queued",
    "message": "Transfer request has been queued for processing"
  }
}
```

#### GET /api/transfer/test
Test endpoint to verify service health.

#### GET /api/transfer/queue/status
Get current queue status.

#### GET /api/transfer/queue
Get all queued transfers.

#### POST /api/transfer/queue/process
Process all queued transfers.

#### DELETE /api/transfer/queue
Clear the transfer queue.

### WebSocket Endpoints

#### /ws/otp
WebSocket endpoint for OTP handling.

**Message Format:**
```json
{
  "type": "otp",
  "otp": "123456",
  "ref": "reference-string"
}
```

#### /ws/namabank
WebSocket endpoint for Nam A Bank integration.

## Configuration

The application uses YAML configuration with environment variable support.

### Environment Variables

- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)
- `REDIS_PASSWORD`: Redis password
- `NAMABANK_USERNAME`: Nam A Bank username
- `NAMABANK_PASSWORD`: Nam A Bank password
- `NAMABANK_DEBIT_ACCOUNT`: Debit account number
- `API_KEY`: API key for authentication
- `LOG_LEVEL`: Log level (debug, info, warn, error)

### Configuration File

```yaml
server:
  port: 8888

redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

namabank:
  username: "your-username"
  password: "your-password"
  debit_account: "your-account"
  base_url: "https://ops-api.namabank.com.vn"
  ops_url: "https://ops.namabank.com.vn"

httpclient:
  timeout: 30s

apikey: "your-api-key"
log_level: "info"
```

## Development

### Available Make Commands

```bash
make help          # Show all available commands
make build         # Build the binary
make test          # Run tests
make dev           # Run in development mode
make lint          # Run linter
make format        # Format code
make docker-build  # Build Docker image
make docker-run    # Run with Docker Compose
```

### Testing

Run tests with coverage:
```bash
make test-coverage
```

### Linting

```bash
make lint
```

## Monitoring and Health Checks

- **Health Check**: `GET /api/transfer/test`
- **Queue Status**: `GET /api/transfer/queue/status`
- **Redis Commander**: Available at `http://localhost:8081` when using Docker Compose

## Security

- API key authentication for sensitive endpoints
- Input validation using Go validator
- Secure headers in HTTP responses
- Non-root user in Docker container

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Ensure Redis is running
   - Check Redis host and port configuration

2. **Browser Automation Issues**
   - Ensure Chromium is installed in the container
   - Check browser automation logs

3. **API Authentication Failed**
   - Verify API key in headers
   - Check API key configuration

### Logs

View application logs:
```bash
# Docker Compose
make docker-logs

# Local development
tail -f application.log
```

## Migration from Java

This Go microservice replaces the original Java Spring Boot application with the following mappings:

| Java Component | Go Component |
|----------------|--------------|
| `TransferController` | `handler.TransferHandler` |
| `NamABankService` | `service.BankService` |
| `DequeService` | `service.QueueService` |
| `AppService` | `service.TransferService` |
| Spring WebSocket | `handler.WebSocketHandler` |
| Playwright Java | `pkg/browser` (Chromedp) |
| Spring Boot Config | `internal/config` (Viper) |

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run linting and tests
6. Submit a pull request

## License

[Add your license information here]
