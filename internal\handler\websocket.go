package handler

import (
	"encoding/json"
	"net/http"

	"transfer-service/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

type WebSocketHandler struct {
	bankService *service.BankService
	upgrader    websocket.Upgrader
}

type OTPMessage struct {
	Type string `json:"type"`
	OTP  string `json:"otp"`
	Ref  string `json:"ref"`
}

type SocketMessage struct {
	SocketName string `json:"socketName"`
	Sign       string `json:"sign"`
}

func NewWebSocketHandler(bankService *service.BankService) *WebSocketHandler {
	return &WebSocketHandler{
		bankService: bankService,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Allow connections from any origin in development
				// In production, you should validate the origin
				return true
			},
		},
	}
}

// HandleOTP handles WebSocket connections for OTP processing
func (h *WebSocketHandler) HandleOTP(c *gin.Context) {
	conn, err := h.upgrader.Upgrade(c.<PERSON>, c.Request, nil)
	if err != nil {
		logrus.Errorf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	logrus.Info("WebSocket connection established for OTP handling")

	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logrus.Errorf("WebSocket error: %v", err)
			}
			break
		}

		if messageType == websocket.TextMessage {
			h.handleTextMessage(conn, message)
		}
	}

	logrus.Info("WebSocket connection closed")
}

func (h *WebSocketHandler) handleTextMessage(conn *websocket.Conn, message []byte) {
	logrus.Infof("Received WebSocket message: %s", string(message))

	// Try to parse as OTP message
	var otpMsg OTPMessage
	if err := json.Unmarshal(message, &otpMsg); err == nil && otpMsg.Type == "otp" {
		h.handleOTPMessage(conn, &otpMsg)
		return
	}

	// Try to parse as socket credentials message
	var socketMsg SocketMessage
	if err := json.Unmarshal(message, &socketMsg); err == nil && socketMsg.SocketName != "" {
		h.handleSocketMessage(conn, &socketMsg)
		return
	}

	// If it's a raw string that looks like JSON, try to extract socket credentials
	h.handleRawMessage(conn, string(message))
}

func (h *WebSocketHandler) handleOTPMessage(conn *websocket.Conn, msg *OTPMessage) {
	logrus.Infof("Processing OTP: %s with ref: %s", msg.OTP, msg.Ref)

	err := h.bankService.VerifyOTP(msg.OTP, msg.Ref)
	if err != nil {
		logrus.Errorf("OTP verification failed: %v", err)
		h.sendResponse(conn, map[string]interface{}{
			"type":    "otp_result",
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	logrus.Info("OTP verification successful")
	h.sendResponse(conn, map[string]interface{}{
		"type":    "otp_result",
		"success": true,
		"message": "OTP verified successfully",
	})
}

func (h *WebSocketHandler) handleSocketMessage(conn *websocket.Conn, msg *SocketMessage) {
	logrus.Infof("Processing socket credentials - Socket Name: %s, Sign: %s", msg.SocketName, msg.Sign)
	
	h.bankService.SetSocketCredentials(msg.SocketName, msg.Sign)
	
	h.sendResponse(conn, map[string]interface{}{
		"type":    "socket_result",
		"success": true,
		"message": "Socket credentials updated",
	})
}

func (h *WebSocketHandler) handleRawMessage(conn *websocket.Conn, message string) {
	// Try to extract socket credentials from raw JSON
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(message), &data); err != nil {
		logrus.Warnf("Failed to parse raw message as JSON: %v", err)
		return
	}

	socketName, hasSocketName := data["socketName"].(string)
	sign, hasSign := data["sign"].(string)

	if hasSocketName && hasSign && socketName != "" && sign != "" {
		logrus.Infof("Extracted socket credentials from raw message - Socket Name: %s, Sign: %s", socketName, sign)
		h.bankService.SetSocketCredentials(socketName, sign)
		
		h.sendResponse(conn, map[string]interface{}{
			"type":    "socket_result",
			"success": true,
			"message": "Socket credentials extracted and updated",
		})
	}
}

func (h *WebSocketHandler) sendResponse(conn *websocket.Conn, response interface{}) {
	data, err := json.Marshal(response)
	if err != nil {
		logrus.Errorf("Failed to marshal response: %v", err)
		return
	}

	err = conn.WriteMessage(websocket.TextMessage, data)
	if err != nil {
		logrus.Errorf("Failed to send WebSocket response: %v", err)
	}
}

// HandleNamAWebSocket handles WebSocket connections for Nam A Bank integration
func (h *WebSocketHandler) HandleNamAWebSocket(c *gin.Context) {
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logrus.Errorf("Failed to upgrade connection: %v", err)
		return
	}
	defer conn.Close()

	logrus.Info("WebSocket connection established for Nam A Bank integration")

	for {
		messageType, message, err := conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				logrus.Errorf("WebSocket error: %v", err)
			}
			break
		}

		if messageType == websocket.TextMessage {
			logrus.Infof("Nam A Bank WebSocket message: %s", string(message))
			// Process Nam A Bank specific messages here
			h.handleNamAMessage(conn, message)
		}
	}

	logrus.Info("Nam A Bank WebSocket connection closed")
}

func (h *WebSocketHandler) handleNamAMessage(conn *websocket.Conn, message []byte) {
	// Handle Nam A Bank specific WebSocket messages
	// This could include login responses, transaction updates, etc.
	logrus.Infof("Processing Nam A Bank message: %s", string(message))
	
	// Parse and handle the message based on its content
	var data map[string]interface{}
	if err := json.Unmarshal(message, &data); err != nil {
		logrus.Warnf("Failed to parse Nam A Bank message: %v", err)
		return
	}

	// Handle different types of messages from Nam A Bank
	if code, exists := data["code"]; exists {
		if codeStr, ok := code.(string); ok && codeStr == "2000" {
			// Successful login response
			if authData, exists := data["data"].(map[string]interface{}); exists {
				if auth, exists := authData["auth"].(map[string]interface{}); exists {
					if deviceID, exists := auth["deviceID"].(string); exists {
						if token, exists := auth["token"].(string); exists {
							h.bankService.SetSession(deviceID, token)
							logrus.Info("Bank session updated from WebSocket message")
						}
					}
				}
			}
		}
	}
}
