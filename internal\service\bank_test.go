package service

import (
	"net/http"
	"testing"
	"time"

	"transfer-service/internal/config"
	"transfer-service/pkg/httpclient"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockHTTPClient is a mock implementation of httpclient.HTTPClient
type MockHTTPClient struct {
	mock.Mock
}

func (m *MockHTTPClient) Get(url string, options *httpclient.RequestOptions) (*http.Response, error) {
	args := m.Called(url, options)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockHTTPClient) Post(url string, body interface{}, options *httpclient.RequestOptions) (*http.Response, error) {
	args := m.Called(url, body, options)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockHTTPClient) Put(url string, body interface{}, options *httpclient.RequestOptions) (*http.Response, error) {
	args := m.Called(url, body, options)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockHTTPClient) Delete(url string, options *httpclient.RequestOptions) (*http.Response, error) {
	args := m.Called(url, options)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockHTTPClient) PostJSON(url string, data interface{}, headers map[string]string) ([]byte, error) {
	args := m.Called(url, data, headers)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockHTTPClient) ReadResponseBody(resp *http.Response) ([]byte, error) {
	args := m.Called(resp)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockHTTPClient) PostJSON(url string, data interface{}, headers map[string]string) ([]byte, error) {
	args := m.Called(url, data, headers)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockHTTPClient) Post(url string, data interface{}, options *httpclient.RequestOptions) (*httpclient.Response, error) {
	args := m.Called(url, data, options)
	return args.Get(0).(*httpclient.Response), args.Error(1)
}

func TestBankService_IsLoggedIn(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "test",
		Password: "test",
	}
	
	bankService := NewBankService(mockHTTP, config)

	// Initially should not be logged in
	assert.False(t, bankService.IsLoggedIn())

	// Set session as logged in
	bankService.sessionMutex.Lock()
	bankService.session.IsLoggedIn = true
	bankService.session.LoginTime = time.Now()
	bankService.sessionMutex.Unlock()

	assert.True(t, bankService.IsLoggedIn())
}

func TestBankService_GetSessionAge(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "test",
		Password: "test",
	}
	
	bankService := NewBankService(mockHTTP, config)

	// When not logged in, session age should be 0
	assert.Equal(t, time.Duration(0), bankService.GetSessionAge())

	// Set session as logged in 5 minutes ago
	loginTime := time.Now().Add(-5 * time.Minute)
	bankService.sessionMutex.Lock()
	bankService.session.IsLoggedIn = true
	bankService.session.LoginTime = loginTime
	bankService.sessionMutex.Unlock()

	sessionAge := bankService.GetSessionAge()
	assert.True(t, sessionAge >= 4*time.Minute && sessionAge <= 6*time.Minute)
}

func TestBankService_EnsureLoggedIn_AlreadyLoggedIn(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "test",
		Password: "test",
	}
	
	bankService := NewBankService(mockHTTP, config)

	// Set session as recently logged in
	bankService.sessionMutex.Lock()
	bankService.session.IsLoggedIn = true
	bankService.session.LoginTime = time.Now().Add(-5 * time.Minute) // 5 minutes ago
	bankService.sessionMutex.Unlock()

	// Should not need to login again
	err := bankService.EnsureLoggedIn()
	assert.NoError(t, err)
}

func TestBankService_EnsureLoggedIn_SessionExpired(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "test",
		Password: "test",
		BaseURL:  "https://test.com",
	}
	
	bankService := NewBankService(mockHTTP, config)

	// Set session as expired (older than 30 minutes)
	bankService.sessionMutex.Lock()
	bankService.session.IsLoggedIn = true
	bankService.session.LoginTime = time.Now().Add(-35 * time.Minute) // 35 minutes ago
	bankService.sessionMutex.Unlock()

	// Mock successful login response
	loginResponse := `{"code":"2000","data":{"auth":{"deviceID":"test-device","token":"test-token"}}}`
	mockHTTP.On("PostJSON", "https://test.com/api/auth/login", mock.Anything, mock.Anything).Return([]byte(loginResponse), nil)

	// Should attempt to login
	err := bankService.EnsureLoggedIn()
	// This will fail because browser automation is not available in test, but that's expected
	assert.Error(t, err)
}

func TestBankService_LoginWithCredentials_Success(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "testuser",
		Password: "testpass",
		BaseURL:  "https://test.com",
	}
	
	bankService := NewBankService(mockHTTP, config)

	// Mock successful login response
	loginResponse := `{"code":"2000","data":{"auth":{"deviceID":"test-device-123","token":"test-token-456"}},"message":"Login successful"}`
	mockHTTP.On("PostJSON", "https://test.com/api/auth/login", mock.Anything, mock.Anything).Return([]byte(loginResponse), nil)

	err := bankService.LoginWithCredentials()
	assert.NoError(t, err)
	assert.True(t, bankService.IsLoggedIn())

	// Check session data
	bankService.sessionMutex.RLock()
	assert.Equal(t, "test-device-123", bankService.session.DeviceID)
	assert.Equal(t, "test-token-456", bankService.session.Token)
	bankService.sessionMutex.RUnlock()

	mockHTTP.AssertExpectations(t)
}

func TestBankService_LoginWithCredentials_Failed(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "testuser",
		Password: "wrongpass",
		BaseURL:  "https://test.com",
	}
	
	bankService := NewBankService(mockHTTP, config)

	// Mock failed login response
	loginResponse := `{"code":"4001","message":"Invalid credentials"}`
	mockHTTP.On("PostJSON", "https://test.com/api/auth/login", mock.Anything, mock.Anything).Return([]byte(loginResponse), nil)

	err := bankService.LoginWithCredentials()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "login failed with code: 4001")
	assert.False(t, bankService.IsLoggedIn())

	mockHTTP.AssertExpectations(t)
}

func TestBankService_generateDeviceID(t *testing.T) {
	mockHTTP := new(MockHTTPClient)
	config := config.NamABankConfig{
		Username: "testuser",
	}
	
	bankService := NewBankService(mockHTTP, config)

	deviceID1 := bankService.generateDeviceID()
	deviceID2 := bankService.generateDeviceID()

	// Device IDs should be different (due to timestamp)
	assert.NotEqual(t, deviceID1, deviceID2)
	
	// Device ID should be 16 characters long
	assert.Equal(t, 16, len(deviceID1))
	assert.Equal(t, 16, len(deviceID2))
}
