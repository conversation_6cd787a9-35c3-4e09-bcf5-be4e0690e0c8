package com.example.transfer.config;

import com.example.transfer.dto.Status;
import com.example.transfer.dto.request.TransferRequest;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AppContext {

    private static String ref;
    private static String creditAccount;
    private static String bankId;
    private static long amount;
    private static String idRequest;
    private static boolean isNew;
    private static String content;
    private static final AtomicLong lastTransferTime = new AtomicLong(0);
    private static final AtomicInteger status = new AtomicInteger(0);

    public static int getStatus() {
        return status.get();
    }

    public static void setStatus(int status) {
        AppContext.status.set(status);
    }

    public static void setLastTransfer(long time) {
        lastTransferTime.set(time);
    }

    public static long getLastTransfer() {
        return lastTransferTime.get();
    }

    public static void setCurrentTime() {
        lastTransferTime.set(System.currentTimeMillis());
    }

    public static boolean isTimeToTransfer() {
        return System.currentTimeMillis() - lastTransferTime.get() > 60000;
    }


    public static String getRef() {
        return ref;
    }


    public static void setRef(String ref) {
        AppContext.ref = ref;
    }

    public static String getCreditAccount() {
        return creditAccount;
    }

    public static void setCreditAccount(String creditAccount) {
        AppContext.creditAccount = creditAccount;
    }

    public static String getBankId() {
        return bankId;
    }

    public static void setBankId(String bankId) {
        AppContext.bankId = bankId;
    }

    public static long getAmount() {
        return amount;
    }

    public static void setAmount(long amount) {
        AppContext.amount = amount;
    }

    public static boolean getIsNew() {
        return isNew;
    }

    public static void setIsNew(boolean isNew) {
        AppContext.isNew = isNew;
    }

    public static String getContent() {return content;}

    public static void setContent(String content) { AppContext.content = content; }

    public static String getIdRequest() {
        return idRequest;
    }

    public static void setIdRequest(String idRequest) {
        AppContext.idRequest = idRequest;
    }

    public static void clear() {
        ref = null;
        creditAccount = null;
        bankId = null;
        amount = 0;
        isNew = false;
        idRequest = null;
        status.set(Status.WAITING.ordinal());
    }

    public static void setFromRequest(TransferRequest request) {
        creditAccount = request.getCrAccount();
        bankId = request.getBankId();
        amount = request.getAmount();
        isNew = true;
        status.set(Status.GET_INFO_BANK.ordinal());
    }
}
