version: '3.8'

services:
  transfer-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: transfer-service
    ports:
      - "8888:8888"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - NAMABANK_USERNAME=${NAMABANK_USERNAME:-hoangminh9119}
      - NAMABANK_PASSWORD=${NAMABANK_PASSWORD:-Minhhoang93@}
      - NAMABANK_DEBIT_ACCOUNT=${NAMABANK_DEBIT_ACCOUNT:-**********}
      - API_KEY=${API_KEY:-WtxjmFWzPy1ZeZZAnr5EVUDUMis1AEkxb6xhfybuRiNCOS5zEx1gdRlRPPDbFMAdgiAgQf2Dj5q8siCXV1H23h0xp1qg0LpngMQYEayMvFfZz}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - transfer-network
    volumes:
      - ./configs:/root/configs:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8888/api/transfer/test"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  redis:
    image: redis:7-alpine
    container_name: transfer-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
    restart: unless-stopped
    networks:
      - transfer-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: transfer-redis-commander
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - transfer-network

volumes:
  redis-data:
    driver: local

networks:
  transfer-network:
    driver: bridge
