package com.example.transfer.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Component;

@Component
public class JsonUtils {
    private final ObjectMapper objectMapper;

    public JsonUtils(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    public String objectToJsonString(Object o) {
        try {
            return objectMapper.writeValueAsString(o);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return null;
        }
    }

    public JsonNode stringToJsonNode(String s) {
        try {
            return objectMapper.readTree(s);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public <T> T jsonStringToObject(String s, TypeReference<T> t) {
        try {
            return objectMapper.readValue(s, t);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public <T> T jsonStringToObject(String s, Class<T> tClass) {
        try {
            return objectMapper.readValue(s, tClass);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}
