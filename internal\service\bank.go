package service

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"transfer-service/internal/config"
	"transfer-service/internal/model"
	"transfer-service/pkg/httpclient"

	"github.com/sirupsen/logrus"
)

type BankService struct {
	httpClient   *httpclient.Client
	config       config.NamABankConfig
	session      *BankSession
	sessionMutex sync.RWMutex
}

type BankSession struct {
	DeviceID   string
	Token      string
	RequestID  string
	SocketName string
	Sign       string
	IsLoggedIn bool
	LoginTime  time.Time
}

func NewBankService(httpClient *httpclient.Client, config config.NamABankConfig) *BankService {
	return &BankService{
		httpClient: httpClient,
		config:     config,
		session:    &BankSession{},
	}
}

func (s *BankService) IsLoggedIn() bool {
	s.sessionMutex.RLock()
	defer s.sessionMutex.RUnlock()
	return s.session.IsLoggedIn
}

func (s *BankService) ProcessExpiredToken() {
	s.sessionMutex.Lock()
	defer s.sessionMutex.Unlock()

	logrus.Info("Processing expired token")
	s.session = &BankSession{}
}

func (s *BankService) GetInfoBankTransfer(accountNumber, bankID string) error {
	if accountNumber == "" || bankID == "" {
		return fmt.Errorf("account number or bank ID is required")
	}

	request := model.BankInfoTransferRequest{
		Number:       accountNumber,
		BankID:       bankID,
		Type:         0,
		Channel:      "99",
		DebitAccount: s.config.DebitAccount,
	}

	headers := s.buildHeaders()
	if headers == nil {
		return fmt.Errorf("not logged in")
	}

	url := fmt.Sprintf("%s/user/information", s.config.BaseURL)
	resp, err := s.httpClient.Post(url, request, &httpclient.RequestOptions{
		Headers: headers,
	})
	if err != nil {
		return fmt.Errorf("failed to get bank info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := s.httpClient.ReadResponseBody(resp)
		logrus.Errorf("Get bank info failed: %d - %s", resp.StatusCode, string(body))
		return fmt.Errorf("get bank info failed with status: %d", resp.StatusCode)
	}

	var idResponse model.IDRequest
	if err := s.httpClient.DecodeJSONResponse(resp, &idResponse); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	s.sessionMutex.Lock()
	s.session.RequestID = idResponse.IDRequest
	s.sessionMutex.Unlock()

	logrus.Infof("Bank info retrieved successfully, request ID: %s", idResponse.IDRequest)
	return nil
}

func (s *BankService) CheckBlackList(accountNumber, bankID string) error {
	if accountNumber == "" || bankID == "" {
		return fmt.Errorf("account number or bank ID is required")
	}

	s.sessionMutex.RLock()
	requestID := s.session.RequestID
	s.sessionMutex.RUnlock()

	if requestID == "" {
		return fmt.Errorf("request ID is required")
	}

	request := model.BlackListRequest{
		BankCiID:      bankID,
		AccountNumber: accountNumber,
		ChannelID:     "NAPAS",
		IDRequest:     requestID,
	}

	headers := s.buildHeaders()
	if headers == nil {
		return fmt.Errorf("not logged in")
	}

	url := fmt.Sprintf("%s/user/checkBlackList", s.config.BaseURL)
	resp, err := s.httpClient.Post(url, request, &httpclient.RequestOptions{
		Headers: headers,
	})
	if err != nil {
		return fmt.Errorf("failed to check blacklist: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := s.httpClient.ReadResponseBody(resp)
		logrus.Errorf("Check blacklist failed: %d - %s", resp.StatusCode, string(body))
		return fmt.Errorf("check blacklist failed with status: %d", resp.StatusCode)
	}

	logrus.Info("Blacklist check completed successfully")
	return nil
}

func (s *BankService) TransferFee(amount int64) error {
	if amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	s.sessionMutex.RLock()
	requestID := s.session.RequestID
	s.sessionMutex.RUnlock()

	if requestID == "" {
		return fmt.Errorf("request ID is required")
	}

	request := model.TransferFeeRequest{
		Amount:          amount,
		DebitAccount:    s.config.DebitAccount,
		IDRequest:       requestID,
		Currency:        "VND",
		TransactionType: "NAPAS",
	}

	headers := s.buildHeaders()
	if headers == nil {
		return fmt.Errorf("not logged in")
	}

	url := fmt.Sprintf("%s/transfer/fee", s.config.BaseURL)
	resp, err := s.httpClient.Post(url, request, &httpclient.RequestOptions{
		Headers: headers,
	})
	if err != nil {
		return fmt.Errorf("failed to calculate transfer fee: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := s.httpClient.ReadResponseBody(resp)
		logrus.Errorf("Transfer fee calculation failed: %d - %s", resp.StatusCode, string(body))
		return fmt.Errorf("transfer fee calculation failed with status: %d", resp.StatusCode)
	}

	logrus.Info("Transfer fee calculated successfully")
	return nil
}

func (s *BankService) buildHeaders() map[string]string {
	s.sessionMutex.RLock()
	defer s.sessionMutex.RUnlock()

	if s.session.Token == "" || s.session.DeviceID == "" {
		return nil
	}

	return map[string]string{
		"token":              s.session.Token,
		"device-id":          s.session.DeviceID,
		"checking":           s.generateChecking(),
		"Origin":             s.config.OPSURL,
		"Referer":            s.config.OPSURL + "/",
		"Host":               strings.Replace(s.config.BaseURL, "https://", "", 1),
		"Sec-Fetch-Dest":     "empty",
		"Sec-Fetch-Mode":     "cors",
		"Sec-Fetch-Site":     "same-site",
		"sec-ch-ua":          `"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"`,
		"sec-ch-ua-mobile":   "?0",
		"sec-ch-ua-platform": `"Windows"`,
	}
}

func (s *BankService) generateChecking() string {
	now := time.Now()
	timestamp := strconv.FormatInt(now.UnixMilli(), 10)
	data := fmt.Sprintf("%s%s", timestamp, "your-secret-key") // Replace with actual secret
	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}

func (s *BankService) TransferNapas(creditAccount, bankID, creditName string, amount int64, content string) error {
	if creditAccount == "" || bankID == "" || creditName == "" {
		return fmt.Errorf("credit account, bank ID, and credit name are required")
	}
	if amount <= 0 {
		return fmt.Errorf("amount must be greater than 0")
	}

	s.sessionMutex.RLock()
	requestID := s.session.RequestID
	s.sessionMutex.RUnlock()

	if requestID == "" {
		return fmt.Errorf("request ID is required")
	}

	request := model.TransferNapasRequest{
		CreditAccount:       creditAccount,
		BankCode:            bankID,
		CreditName:          creditName,
		TransactionAmount:   strconv.FormatInt(amount, 10),
		TransactionCurrency: "VND",
		TransactionDetail:   content,
		TypeDescription:     0,
		DebitAccount:        s.config.DebitAccount,
		FeKey:               s.generateChecking(),
		IDRequest:           requestID,
	}

	headers := s.buildHeaders()
	if headers == nil {
		return fmt.Errorf("not logged in")
	}

	url := fmt.Sprintf("%s/transfer/napas", s.config.BaseURL)
	resp, err := s.httpClient.Post(url, request, &httpclient.RequestOptions{
		Headers: headers,
	})
	if err != nil {
		return fmt.Errorf("failed to transfer via NAPAS: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := s.httpClient.ReadResponseBody(resp)
		logrus.Errorf("NAPAS transfer failed: %d - %s", resp.StatusCode, string(body))
		return fmt.Errorf("NAPAS transfer failed with status: %d", resp.StatusCode)
	}

	body, err := s.httpClient.ReadResponseBody(resp)
	if err != nil {
		return fmt.Errorf("failed to read response: %w", err)
	}

	logrus.Infof("NAPAS transfer response: %s", string(body))
	return nil
}

func (s *BankService) VerifyOTP(otp, ref string) error {
	if otp == "" {
		return fmt.Errorf("OTP is required")
	}

	s.sessionMutex.RLock()
	requestID := s.session.RequestID
	s.sessionMutex.RUnlock()

	if requestID == "" {
		return fmt.Errorf("request ID is required")
	}

	if ref == "" {
		// Wait a bit for ref to be available
		time.Sleep(1 * time.Second)
		if ref == "" {
			return fmt.Errorf("ref is required")
		}
	}

	request := model.OTPRequest{
		IDRequest: requestID,
		CodeOTP:   otp,
		Ref:       ref,
	}

	headers := s.buildHeaders()
	if headers == nil {
		return fmt.Errorf("not logged in")
	}

	url := fmt.Sprintf("%s/auth/otp", s.config.BaseURL)
	resp, err := s.httpClient.Post(url, request, &httpclient.RequestOptions{
		Headers: headers,
	})
	if err != nil {
		return fmt.Errorf("failed to verify OTP: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := s.httpClient.ReadResponseBody(resp)
		logrus.Errorf("OTP verification failed: %d - %s", resp.StatusCode, string(body))
		return fmt.Errorf("OTP verification failed with status: %d", resp.StatusCode)
	}

	var response model.NamAResponse
	if err := s.httpClient.DecodeJSONResponse(resp, &response); err != nil {
		return fmt.Errorf("failed to decode OTP response: %w", err)
	}

	if response.Code == 2099 {
		logrus.Info("OTP verification successful")
		s.sessionMutex.Lock()
		s.session.RequestID = ""
		s.sessionMutex.Unlock()
		return nil
	}

	if response.Code == 4031 {
		return fmt.Errorf("OTP verification failed: invalid OTP")
	}

	return fmt.Errorf("OTP verification failed with code: %d", response.Code)
}

func (s *BankService) GetTransactions() error {
	now := time.Now()
	fifteenDaysAgo := now.AddDate(0, 0, -15)

	request := model.TransactionRequest{
		AccountNumber: s.config.DebitAccount,
		FromDate:      fifteenDaysAgo.UnixMilli(),
		ToDate:        now.UnixMilli(),
		Page:          1,
		Size:          100,
		TxnTypes:      []string{},
	}

	headers := s.buildHeaders()
	if headers == nil {
		return fmt.Errorf("not logged in")
	}

	url := fmt.Sprintf("%s/user/transactions", s.config.BaseURL)
	resp, err := s.httpClient.Post(url, request, &httpclient.RequestOptions{
		Headers: headers,
	})
	if err != nil {
		return fmt.Errorf("failed to get transactions: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := s.httpClient.ReadResponseBody(resp)
		logrus.Errorf("Get transactions failed: %d - %s", resp.StatusCode, string(body))
		return fmt.Errorf("get transactions failed with status: %d", resp.StatusCode)
	}

	body, err := s.httpClient.ReadResponseBody(resp)
	if err != nil {
		return fmt.Errorf("failed to read transactions response: %w", err)
	}

	logrus.Infof("Transactions response: %s", string(body))
	return nil
}

func (s *BankService) SetSession(deviceID, token string) {
	s.sessionMutex.Lock()
	defer s.sessionMutex.Unlock()

	s.session.DeviceID = deviceID
	s.session.Token = token
	s.session.IsLoggedIn = true
	s.session.LoginTime = time.Now()

	logrus.Infof("Session updated - Device ID: %s, Token: %s", deviceID, token)
}

func (s *BankService) SetSocketCredentials(socketName, sign string) {
	s.sessionMutex.Lock()
	defer s.sessionMutex.Unlock()

	s.session.SocketName = socketName
	s.session.Sign = sign

	logrus.Infof("Socket credentials updated - Socket Name: %s, Sign: %s", socketName, sign)
}
