# Transfer Service API Documentation

## Overview

The Transfer Service provides REST API endpoints for managing bank transfer operations through a queue-based system. All endpoints return JSON responses with a consistent format.

## Base URL

```
http://localhost:8888
```

## Authentication

Most endpoints require API key authentication via the `X-Api-Key` header.

```
X-Api-Key: your-api-key-here
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Optional response data
  "error": "Error details" // Only present on errors
}
```

## Endpoints

### Transfer Operations

#### POST /api/transfer

Queue a new transfer request for processing.

**Authentication:** Required

**Request Headers:**
```
Content-Type: application/json
X-Api-Key: your-api-key
```

**Request Body:**
```json
{
  "crAccount": "**********",
  "bankId": "970407", 
  "amount": 100000,
  "content": "Transfer description"
}
```

**Request Body Parameters:**
- `crAccount` (string, required): Credit account number
- `bankId` (string, required): Bank ID code
- `amount` (integer, required): Transfer amount in VND (minimum: 1)
- `content` (string, required): Transfer description/content

**Response:**
```json
{
  "success": true,
  "message": "Transfer request queued successfully",
  "data": {
    "transferId": "",
    "status": "queued",
    "message": "Transfer request has been queued for processing"
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or missing API key
- `400 Bad Request`: Invalid request format or validation errors
- `500 Internal Server Error`: Server error while queuing transfer

---

#### GET /api/transfer/test

Test endpoint to verify service health.

**Authentication:** Not required

**Response:**
```json
{
  "success": true,
  "message": "Test endpoint working",
  "data": "Test"
}
```

---

### Queue Management

#### GET /api/transfer/queue/status

Get current queue status information.

**Authentication:** Not required

**Response:**
```json
{
  "success": true,
  "message": "Queue status retrieved successfully",
  "data": {
    "queueSize": 5,
    "isEmpty": false
  }
}
```

**Response Data:**
- `queueSize` (integer): Number of transfers currently in queue
- `isEmpty` (boolean): Whether the queue is empty

---

#### GET /api/transfer/queue

Get all transfers currently in the queue.

**Authentication:** Not required

**Response:**
```json
{
  "success": true,
  "message": "Queued transfers retrieved successfully",
  "data": [
    {
      "crAccount": "**********",
      "bankId": "970407",
      "amount": 100000,
      "content": "Transfer 1"
    },
    {
      "crAccount": "**********", 
      "bankId": "970415",
      "amount": 200000,
      "content": "Transfer 2"
    }
  ]
}
```

---

#### POST /api/transfer/queue/process

Process all transfers currently in the queue.

**Authentication:** Required

**Request Headers:**
```
X-Api-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "message": "Queue processed successfully"
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or missing API key
- `500 Internal Server Error`: Error processing queue

---

#### DELETE /api/transfer/queue

Clear all transfers from the queue.

**Authentication:** Required

**Request Headers:**
```
X-Api-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "message": "Queue cleared successfully"
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or missing API key
- `500 Internal Server Error`: Error clearing queue

---

### Authentication & Login

#### POST /api/transfer/login

Manually trigger login to Nam A Bank. The system will attempt browser automation login first, then fallback to API login if needed.

**Authentication:** Required

**Request Headers:**
```
X-Api-Key: your-api-key
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "loginTime": "2024-01-01T10:00:00Z",
    "isLoggedIn": true
  }
}
```

**Error Responses:**
- `401 Unauthorized`: Invalid or missing API key
- `500 Internal Server Error`: Login failed

---

#### GET /api/transfer/login/status

Get current login status and session information.

**Authentication:** Not required

**Response:**
```json
{
  "success": true,
  "message": "Login status retrieved",
  "data": {
    "isLoggedIn": true,
    "loginTime": "2024-01-01T10:00:00Z",
    "sessionAge": "00h15m30s"
  }
}
```

**Response Data:**
- `isLoggedIn` (boolean): Whether currently logged in to Nam A Bank
- `loginTime` (string): Timestamp of last successful login
- `sessionAge` (string): How long since last login

---

## WebSocket Endpoints

### /ws/otp

WebSocket endpoint for real-time OTP handling.

**Connection:** WebSocket

**Message Types:**

#### OTP Verification
Send OTP for verification:
```json
{
  "type": "otp",
  "otp": "123456",
  "ref": "reference-string"
}
```

**Response:**
```json
{
  "type": "otp_result",
  "success": true,
  "message": "OTP verified successfully"
}
```

#### Socket Credentials
Send socket credentials:
```json
{
  "socketName": "socket-name",
  "sign": "signature-string"
}
```

**Response:**
```json
{
  "type": "socket_result", 
  "success": true,
  "message": "Socket credentials updated"
}
```

---

### /ws/namabank

WebSocket endpoint for Nam A Bank integration.

**Connection:** WebSocket

Handles Nam A Bank specific messages including login responses and transaction updates.

---

## Error Codes

| HTTP Status | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input or validation error |
| 401 | Unauthorized - Invalid or missing API key |
| 500 | Internal Server Error - Server-side error |

## Rate Limiting

Currently no rate limiting is implemented. Consider implementing rate limiting for production use.

## Examples

### cURL Examples

#### Queue a Transfer
```bash
curl -X POST http://localhost:8888/api/transfer \
  -H "Content-Type: application/json" \
  -H "X-Api-Key: your-api-key" \
  -d '{
    "crAccount": "**********",
    "bankId": "970407",
    "amount": 100000,
    "content": "Test transfer"
  }'
```

#### Check Queue Status
```bash
curl http://localhost:8888/api/transfer/queue/status
```

#### Process Queue
```bash
curl -X POST http://localhost:8888/api/transfer/queue/process \
  -H "X-Api-Key: your-api-key"
```

### JavaScript Examples

#### Queue a Transfer
```javascript
const response = await fetch('http://localhost:8888/api/transfer', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Api-Key': 'your-api-key'
  },
  body: JSON.stringify({
    crAccount: '**********',
    bankId: '970407', 
    amount: 100000,
    content: 'Test transfer'
  })
});

const result = await response.json();
console.log(result);
```

#### WebSocket Connection
```javascript
const ws = new WebSocket('ws://localhost:8888/ws/otp');

ws.onopen = function() {
  console.log('WebSocket connected');
};

ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};

// Send OTP
ws.send(JSON.stringify({
  type: 'otp',
  otp: '123456',
  ref: 'reference-string'
}));
```

## Monitoring

- Health check endpoint: `GET /api/transfer/test`
- Queue monitoring: `GET /api/transfer/queue/status`
- Application logs provide detailed operation information

## Security Considerations

- Always use HTTPS in production
- Secure API key storage and rotation
- Input validation is performed on all endpoints
- Consider implementing rate limiting
- Monitor for suspicious activity
